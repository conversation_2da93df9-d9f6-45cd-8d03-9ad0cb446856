import os
import logging
from dotenv import load_dotenv
import time
from fastapi.responses import JSONResponse, PlainTextResponse
from app.services.rabbitmq_manager import rabbitmq_manager
from fastapi.security import HTTPAuthorizationCredentials
from app.logging_config import setup_logging

# Load environment variables at the very beginning
load_dotenv(verbose=True)

# Set up logging first
log_level = os.getenv("LOG_LEVEL", "INFO")
logger = setup_logging(
    log_level=getattr(logging, log_level.upper())
)
logger.info(f"Setting up logging with level: {log_level}")
logger.info("Logging system initialized successfully")

# Log the loaded environment variables (without secrets)
logger.info("Environment variables loaded:")
logger.info(f"AWS_ACCESS_KEY_ID present: {os.getenv('AWS_ACCESS_KEY_ID') is not None}")
logger.info(f"AWS_SECRET_ACCESS_KEY present: {os.getenv('AWS_SECRET_ACCESS_KEY') is not None}")
logger.info(f"AWS_REGION: {os.getenv('AWS_REGION')}")
logger.info(f"AWS_S3_BUCKET_NAME: {os.getenv('AWS_S3_BUCKET_NAME')}")

from fastapi import FastAPI, Depends, Request, Response, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.openapi.models import SecuritySchemeType, SecurityScheme
from app.routers import chatbot
from app.database import engine, Base
from app.dependencies import get_auth_context, AuthContext, security
from prometheus_client import make_asgi_app
from app.monitoring import REQUEST_COUNT, LatencyTimer, ACTIVE_CONNECTIONS, AUTH_FAILURE_COUNT
import uuid
from fastapi.responses import RedirectResponse
from app.error_handlers import register_error_handlers
from app.startup_events import lifespan

# Database tables will be created in the lifespan context manager

app = FastAPI(
    title="Knowledge Base API",
    description="API for managing knowledge base and chatbot interactions",
    version="1.0.0",
    openapi_tags=[
        {"name": "chatbot", "description": "Chatbot operations"}
    ],
    swagger_ui_parameters={
        "persistAuthorization": True,
        "displayRequestDuration": True,
        "filter": True,
        "tryItOutEnabled": True
    },
    openapi_url="/v2/api-docs",  # Customize the OpenAPI JSON URL
    lifespan=lifespan
)

# Override the default OpenAPI schema to add security scheme
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add security scheme
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT token"
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    # Add additional metadata if needed
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# Create metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Register error handlers
register_error_handlers(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify the allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    start_time = time.time()
    
    # Generate request ID
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    
    # Add request ID to request state
    request.state.request_id = request_id
    
    # Log request
    logger.info(
        f"Request started",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "client_ip": request.client.host,
            "user_agent": request.headers.get("User-Agent", "Unknown")
        }
    )
    
    # Process request
    try:
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Request completed",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time_ms": round(process_time * 1000, 2)
            }
        )
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response
    except Exception as e:
        # Log exception
        logger.info(
            f"Request failed: {str(e)}",
            exc_info=True,
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path
            }
        )
        raise

# Add Prometheus middleware
@app.middleware("http")
async def prometheus_middleware(request: Request, call_next):
    # Skip metrics endpoint to avoid recursion
    if request.url.path in ["/metrics", "/health"]:
        return await call_next(request)
    
    # Increment active connections
    ACTIVE_CONNECTIONS.inc()
    
    # Track request latency
    method = request.method
    endpoint = request.url.path
    
    with LatencyTimer(method, endpoint):
        # Process the request
        response = await call_next(request)
    
    # Record request count
    REQUEST_COUNT.labels(
        method=method,
        endpoint=endpoint,
        status_code=response.status_code
    ).inc()
    
    # Decrement active connections
    ACTIVE_CONNECTIONS.dec()
    
    return response

# Add authentication middleware
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # Skip authentication for certain paths
    skip_auth_paths = [
        "/",
        "/docs",
        "/openapi.json",
        "/v2/api-docs",
        "/metrics",
        "/health"
    ]
    
    # Check exact path match
    if request.url.path in skip_auth_paths:
        response = await call_next(request)
        return response
    
    # Check for path prefixes that should skip auth
    skip_auth_prefixes = ["/docs", "/openapi"]
    if any(request.url.path.startswith(prefix) for prefix in skip_auth_prefixes):
        response = await call_next(request)
        return response
    
    # Get Authorization header
    authorization = request.headers.get("Authorization")
    if not authorization:
        AUTH_FAILURE_COUNT.labels(reason="missing_header").inc()
        logger.warning(
            "Authentication failed: Missing Authorization header",
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "path": request.url.path,
                "client_ip": request.client.host
            }
        )
        return Response(
            content='{"detail":"Authorization header missing"}',
            status_code=401,
            media_type="application/json",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    try:
        # Extract token from Authorization header
        if authorization.startswith("Bearer "):
            token = authorization.replace("Bearer ", "")
        else:
            AUTH_FAILURE_COUNT.labels(reason="invalid_scheme").inc()
            logger.warning(
                "Authentication failed: Invalid scheme",
                extra={
                    "request_id": getattr(request.state, "request_id", "unknown"),
                    "path": request.url.path,
                    "client_ip": request.client.host
                }
            )
            return Response(
                content='{"detail":"Invalid authentication scheme. Expected Bearer"}',
                status_code=401,
                media_type="application/json",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Create credentials object
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
        
        # Process the token and store auth context in request.state
        auth_context = await get_auth_context(credentials, request)
        
        # Log successful authentication
        logger.info(
            "Authentication successful",
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "user_id": auth_context.user_id,
                "tenant_id": auth_context.tenant_id
            }
        )
        
        # Continue with the request
        return await call_next(request)
    except Exception as e:
        # Handle authentication errors
        AUTH_FAILURE_COUNT.labels(reason="auth_error").inc()
        logger.info(
            f"Authentication error: {str(e)}",
            exc_info=True,
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "path": request.url.path,
                "client_ip": request.client.host
            }
        )
        return Response(
            content=f'{{"detail":"Authentication error: {str(e)}"}}',
            status_code=401,
            media_type="application/json",
            headers={"WWW-Authenticate": "Bearer"}
        )

# RabbitMQ startup and shutdown handlers
# Note: Startup and shutdown events are now handled by the lifespan context manager
# in app/startup_events.py which includes the idle conversation monitor

# Health check endpoint for Kubernetes probes
@app.get("/health")
async def health_check():
    """
    Health check endpoint for Kubernetes probes
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "service": "sd-whatsapp-chatbot",
            "version": "1.0.0"
        }
        
        # Optional: Add RabbitMQ health check
        try:
            rabbitmq_status = await rabbitmq_manager.get_status()
            health_status["rabbitmq"] = {
                "initialized": rabbitmq_status.get("manager", {}).get("is_initialized", False),
                "running": rabbitmq_status.get("manager", {}).get("is_running", False)
            }
        except Exception as e:
            health_status["rabbitmq"] = {"error": str(e)}
        
        return JSONResponse(
            status_code=200,
            content=health_status
        )
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time(),
                "service": "sd-whatsapp-chatbot"
            }
        )

# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint
    """
    return {
        "message": "SD WhatsApp Chatbot API",
        "status": "running",
        "version": "1.0.0",
        "docs": "/docs"
    }

# Prometheus metrics endpoint
@app.get("/metrics")
async def metrics():
    """
    Prometheus metrics endpoint
    """
    try:
        from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
        return PlainTextResponse(
            content=generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.info(f"Error generating metrics: {str(e)}")
        return PlainTextResponse(
            content=f"# Error generating metrics: {str(e)}\n",
            status_code=500
        )

# Include routers
app.include_router(chatbot.router)

@app.get("/v2/api-docs", include_in_schema=False)
async def get_openapi_json():
    return app.openapi()