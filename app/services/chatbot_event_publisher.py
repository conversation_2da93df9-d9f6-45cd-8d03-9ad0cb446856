"""
Chatbot Event Publisher Service

Handles publishing chatbot-related events to RabbitMQ for other services to consume.
"""

import json
import logging
from typing import Dict, Any, Optional
from app.services.rabbitmq_service import rabbitmq_service

logger = logging.getLogger(__name__)

class ChatbotEventPublisher:
    """
    Service for publishing chatbot events to RabbitMQ
    """

    def __init__(self):
        """Initialize the event publisher"""
        self.exchange_name = "ex.whatsappChatbot"
        
    async def publish_chatbot_status_updated(
        self,
        chatbot_id: str,
        tenant_id: int,
        status: str,
        account_id: int
    ) -> bool:
        """
        Publish chatbot status updated event

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            status: The new status (DRAFT, ACTIVE, INACTIVE)
            account_id: The connected account ID

        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare event payload
            event_payload = {
                "chatbotId": chatbot_id,
                "tenantId": tenant_id,
                "status": status,
                "accountId": account_id
            }

            # Routing key for status updates
            routing_key = "chatbot.status.updated"

            # Publish the event
            success = await self._publish_event(
                routing_key=routing_key,
                payload=event_payload,
                event_type="chatbot_status_updated"
            )

            if success:
                logger.info(
                    f"Published chatbot status updated event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"status={status}, account_id={account_id}"
                )
            else:
                logger.error(
                    f"Failed to publish chatbot status updated event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"status={status}, account_id={account_id}"
                )

            return success

        except Exception as e:
            logger.error(
                f"Error publishing chatbot status updated event: {str(e)}, "
                f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                f"status={status}, account_id={account_id}"
            )
            return False

    async def publish_status_updated_event(
        self,
        status: str,
        connected_account_id: int,
        connected_account_name: str,
        chatbot_id: str = None,
        tenant_id: int = None
    ) -> bool:
        """
        Publish chatbot status updated event
        
        Args:
            status: The new status (DRAFT, ACTIVE, INACTIVE)
            connected_account_id: The connected account ID
            connected_account_name: The connected account name
            chatbot_id: Optional chatbot ID for logging
            tenant_id: Optional tenant ID for logging
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare event payload
            event_payload = {
                "status": status,
                "connectedAccount": {
                    "id": connected_account_id,
                    "name": connected_account_name
                }
            }
            
            # Routing key for status updates
            routing_key = "chatbot.status.updated"
            
            # Publish the event
            success = await self._publish_event(
                routing_key=routing_key,
                payload=event_payload,
                event_type="chatbot_status_updated"
            )
            
            if success:
                logger.info(
                    f"Published chatbot status updated event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"status={status}, account_id={connected_account_id}"
                )
            else:
                logger.error(
                    f"Failed to publish chatbot status updated event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"status={status}, account_id={connected_account_id}"
                )
                
            return success
            
        except Exception as e:
            logger.error(
                f"Error publishing chatbot status updated event: {str(e)}, "
                f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                f"status={status}, account_id={connected_account_id}"
            )
            return False
    
    async def publish_chatbot_created_event(
        self,
        chatbot_id: str,
        chatbot_name: str,
        connected_account_id: int,
        connected_account_name: str,
        tenant_id: int,
        created_by: str
    ) -> bool:
        """
        Publish chatbot created event
        
        Args:
            chatbot_id: The chatbot ID
            chatbot_name: The chatbot name
            connected_account_id: The connected account ID
            connected_account_name: The connected account name
            tenant_id: The tenant ID
            created_by: User ID who created the chatbot
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare event payload
            event_payload = {
                "chatbotId": chatbot_id,
                "chatbotName": chatbot_name,
                "status": "DRAFT",  # Always DRAFT when created
                "connectedAccount": {
                    "id": connected_account_id,
                    "name": connected_account_name
                },
                "tenantId": tenant_id,
                "createdBy": created_by
            }
            
            # Routing key for chatbot creation
            routing_key = "chatbot.created"
            
            # Publish the event
            success = await self._publish_event(
                routing_key=routing_key,
                payload=event_payload,
                event_type="chatbot_created"
            )
            
            if success:
                logger.info(
                    f"Published chatbot created event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"account_id={connected_account_id}"
                )
            else:
                logger.error(
                    f"Failed to publish chatbot created event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"account_id={connected_account_id}"
                )
                
            return success
            
        except Exception as e:
            logger.error(
                f"Error publishing chatbot created event: {str(e)}, "
                f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                f"account_id={connected_account_id}"
            )
            return False
    
    async def _publish_event(
        self,
        routing_key: str,
        payload: Dict[str, Any],
        event_type: str
    ) -> bool:
        """
        Internal method to publish events to RabbitMQ

        Args:
            routing_key: The routing key for the event
            payload: The event payload
            event_type: Type of event for logging

        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            logger.info(f"🤖 PUBLISHING CHATBOT EVENT - Type: {event_type}")
            logger.info(f"🤖 CHATBOT EVENT - Routing Key: {routing_key}")
            logger.info(f"🤖 CHATBOT EVENT - Exchange: {self.exchange_name}")

            # Log payload details
            logger.info(f"🤖 CHATBOT EVENT - Payload Keys: {list(payload.keys()) if isinstance(payload, dict) else 'Not a dict'}")
            if isinstance(payload, dict):
                # Log important fields without exposing sensitive data
                safe_payload = {k: v for k, v in payload.items() if k not in ['token', 'password', 'secret']}
                logger.info(f"🤖 CHATBOT EVENT - Payload: {safe_payload}")
            else:
                logger.info(f"🤖 CHATBOT EVENT - Payload: {payload}")

            # Convert payload to JSON string
            message_body = json.dumps(payload, default=str)
            logger.info(f"🤖 CHATBOT EVENT - Serialized Size: {len(message_body)} bytes")

            # Publish the message (rabbitmq_service.publish_message now handles reconnection)
            await rabbitmq_service.publish_message(
                exchange=self.exchange_name,
                routing_key=routing_key,
                message=message_body,
                durable=True
            )

            logger.info(f"✅ CHATBOT EVENT PUBLISHED - Type: {event_type}, Routing Key: {routing_key}")
            return True

        except Exception as e:
            logger.error(
                f"Error in _publish_event for {event_type}: {str(e)}, "
                f"routing_key={routing_key}"
            )
            return False
    
    async def ensure_exchange_exists(self) -> bool:
        """
        Ensure the chatbot exchange exists

        Returns:
            bool: True if exchange exists or was created successfully
        """
        try:
            # Ensure connection is available (this will handle reconnection if needed)
            if not await rabbitmq_service._ensure_connection():
                logger.error("Failed to establish RabbitMQ connection")
                return False

            # Declare the exchange (this is idempotent)
            await rabbitmq_service.declare_exchange(
                exchange_name=self.exchange_name,
                exchange_type="topic"  # Topic exchange for routing key patterns
            )

            logger.info(f"Chatbot exchange '{self.exchange_name}' is ready")
            return True

        except Exception as e:
            logger.error(f"Error ensuring chatbot exchange exists: {str(e)}")
            return False
    
    def get_exchange_name(self) -> str:
        """Get the exchange name used for chatbot events"""
        return self.exchange_name
    
    def get_status_routing_key(self) -> str:
        """Get the routing key for status update events"""
        return "chatbot.status.updated"
    
    def get_created_routing_key(self) -> str:
        """Get the routing key for chatbot created events"""
        return "chatbot.created"
    
    async def publish_chatbot_name_updated(
        self,
        chatbot_id: str,
        chatbot_name: str,
        tenant_id: int
    ) -> bool:
        """
        Publish chatbot name updated event
        
        Args:
            chatbot_id: The chatbot ID
            chatbot_name: The new chatbot name
            tenant_id: The tenant ID
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare event payload
            event_payload = {
                "id": chatbot_id,
                "name": chatbot_name,
                "tenantId": tenant_id
            }
            
            # Routing key for name updates
            routing_key = "whatsapp.chatbot.name.updated"
            
            # Publish the event
            success = await self._publish_event(
                routing_key=routing_key,
                payload=event_payload,
                event_type="chatbot_name_updated"
            )
            
            if success:
                logger.info(
                    f"Published chatbot name updated event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"name={chatbot_name}"
                )
            else:
                logger.error(
                    f"Failed to publish chatbot name updated event: "
                    f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                    f"name={chatbot_name}"
                )
                
            return success
            
        except Exception as e:
            logger.error(
                f"Error publishing chatbot name updated event: {str(e)}, "
                f"chatbot_id={chatbot_id}, tenant_id={tenant_id}, "
                f"name={chatbot_name}"
            )
            return False


# Global instance
chatbot_event_publisher = ChatbotEventPublisher()
