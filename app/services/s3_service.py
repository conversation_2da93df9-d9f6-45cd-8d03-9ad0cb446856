import os
import boto3
from botocore.exceptions import ClientError
import logging
from io import BytesIO
from dotenv import load_dotenv
import uuid

# Set up logging
logger = logging.getLogger(__name__)

class S3Service:

    def __init__(self):
        # Load environment variables
        load_dotenv()
        
        # Get credentials from environment variables
        aws_access_key_id = os.getenv("DO_SPACES_KEY")
        aws_secret_access_key = os.getenv("DO_SPACES_SECRET")
        self.region = os.getenv("DO_SPACES_REGION", "nyc3")
        self.bucket_name = os.getenv("DO_SPACES_BUCKET")
        self.endpoint_url = os.getenv("DO_SPACES_ENDPOINT", f"https://{self.region}.digitaloceanspaces.com")
        
        # Log the environment variable status (without revealing secrets)
        logger.info(f"DO_SPACES_KEY present: {aws_access_key_id is not None}")
        logger.info(f"DO_SPACES_SECRET present: {aws_secret_access_key is not None}")
        logger.info(f"DO_SPACES_REGION: {self.region}")
        logger.info(f"DO_SPACES_BUCKET: {self.bucket_name}")
        logger.info(f"DO_SPACES_ENDPOINT: {self.endpoint_url}")
        
        missing_vars = []
        if not aws_access_key_id:
            missing_vars.append("DO_SPACES_KEY")
        if not aws_secret_access_key:
            missing_vars.append("DO_SPACES_SECRET")
        if not self.bucket_name:
            missing_vars.append("DO_SPACES_BUCKET")
        
        if missing_vars:
            error_msg = f"Missing required environment variables: {', '.join(missing_vars)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # Initialize S3 client with DigitalOcean Spaces endpoint
        self.s3_client = boto3.client(
            's3',
            region_name=self.region,
            endpoint_url=self.endpoint_url,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        
        logger.info(f"Initialized S3 service with bucket: {self.bucket_name} at endpoint: {self.endpoint_url}")
    
    def upload_file(self, file_data, tenant_id, chatbot_id, filename, content_type="application/pdf"):
        """
        Upload a file to S3 bucket with the specified folder structure
        
        Args:
            file_data: The file data as bytes or file-like object
            tenant_id: The tenant ID
            chatbot_id: The chatbot ID
            filename: The original filename
            content_type: The content type of the file
        
        Returns:
            The S3 object key (path) where the file was stored
        """
        try:
            # Generate a random string to ensure filename uniqueness
            random_suffix = uuid.uuid4().hex[:8]
            
            # Split filename and extension
            name, ext = os.path.splitext(filename)
            
            # Create unique filename with random suffix
            unique_filename = f"{name}_{random_suffix}{ext}"
            
            # Create the S3 key with the specified folder structure
            s3_key = f"{tenant_id}_knowledgebase/{chatbot_id}/files/{unique_filename}"
            
            # Upload the file to S3
            self.s3_client.upload_fileobj(
                file_data,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': content_type
                }
            )
            
            logger.info(f"Successfully uploaded file to S3: {s3_key}")
            return s3_key
        except ClientError as e:
            logger.error(f"Error uploading file to S3: {str(e)}")
            raise
    
    def download_file(self, s3_key):
        """
        Download a file from S3 bucket
        
        Args:
            s3_key: The S3 object key (path)
            
        Returns:
            The file data as bytes
        """
        try:
            # Create a BytesIO object to store the downloaded file
            file_data = BytesIO()
            
            # Download the file from S3
            self.s3_client.download_fileobj(
                self.bucket_name,
                s3_key,
                file_data
            )
            
            # Reset the file pointer to the beginning
            file_data.seek(0)
            
            logger.info(f"Successfully downloaded file from S3: {s3_key}")
            return file_data
        except ClientError as e:
            logger.error(f"Error downloading file from S3: {str(e)}")
            raise
    
    def delete_file(self, s3_key):
        """
        Delete a file from S3 bucket
        
        Args:
            s3_key: The S3 object key (path)
            
        Returns:
            True if the file was deleted successfully, False otherwise
        """
        try:
            # Delete the file from S3
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            
            logger.info(f"Successfully deleted file from S3: {s3_key}")
            return True
        except ClientError as e:
            logger.error(f"Error deleting file from S3: {str(e)}")
            return False
    
    def get_file_url(self, s3_key, expiration=3600):
        """
        Generate a presigned URL for a file in S3
        
        Args:
            s3_key: The S3 object key (path)
            expiration: The expiration time in seconds (default: 1 hour)
            
        Returns:
            The presigned URL
        """
        try:
            # Generate a presigned URL
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key
                },
                ExpiresIn=expiration
            )
            
            logger.info(f"Generated presigned URL for S3 file: {s3_key}")
            return url
        except ClientError as e:
            logger.error(f"Error generating presigned URL for S3 file: {str(e)}")
            raise