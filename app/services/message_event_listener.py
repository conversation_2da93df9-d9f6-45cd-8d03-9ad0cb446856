"""
Message Event Listener Service

This service handles incoming user messages from the ex.message exchange
and processes them through the conversation flow.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from aio_pika.abc import AbstractIncomingMessage


from app.database import get_db
from app.services.rabbitmq_service import rabbitmq_service
from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.redis_service import RedisService
from app.services.elasticsearch_service import ElasticsearchService
from app.services.charge_calculator import charge_calculator
from app.services.chatbot_service import ChatbotService
from app.services.entity_field_service import EntityFieldService
from app.models import ChatbotConversation, Chatbot
from app.utils.conversation_state_utils import (
    clean_conversation_state,
    update_conversation_in_db,
    store_conversation_turn,
    track_credit_usage
)

logger = logging.getLogger(__name__)


class MessageEventListener:
    """
    Listener for incoming user messages in the WhatsApp chatbot system
    """
    
    def __init__(self):
        self.queue_name = "q.message.chatbot.user.response.chatbot"
        self.routing_key = "message.chatbot.user.response"
        self.exchange_name = "ex.message"
        self.is_running = False
        self.conversation_event_publisher = conversation_event_publisher
    
    def _get_event_extra_data(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get extra data for events including workflow metadata and JWT token.
        
        Args:
            state: Conversation state dictionary
            
        Returns:
            Dict containing extra data for event publishing
        """
        extra_data = {
            "chatbotType": state.get("chatbotType")
        }
        
        # Include workflow metadata if available
        workflow_metadata = state.get("workflow_metadata")
        if workflow_metadata:
            extra_data["metadata"] = workflow_metadata
        
        # Include JWT token if available
        jwt_token = state.get("jwt_token")
        if jwt_token:
            extra_data["jwtToken"] = jwt_token
        
        # Include actual user ID if available (now long integer from IAM)
        actual_user_id = state.get("user_id")
        if actual_user_id:
            extra_data["actualUserId"] = actual_user_id
        
        # Include connected account and entity details
        connected_account = state.get("connected_account")
        if connected_account:
            extra_data["connectedAccount"] = connected_account
            
        entity_details = state.get("entity_details")
        if entity_details:
            extra_data["entityDetails"] = entity_details
        
        return extra_data
    
    async def start(self):
        """
        Start the message event listener
        """
        if self.is_running:
            logger.warning("Message event listener is already running")
            return
        
        try:
            logger.info("Starting message event listener...")
            
            # Connect to RabbitMQ
            connected = await rabbitmq_service.connect()
            if not connected:
                raise RuntimeError("Failed to connect to RabbitMQ")
            
            # Setup the message listener
            await rabbitmq_service.setup_message_listener()

            # Setup publishers for sending conversation responses
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()

            # Register our event handler with the RabbitMQ service
            rabbitmq_service.register_event_handler(
                self.routing_key,
                self.handle_user_message_event
            )

            # Start consuming messages
            await rabbitmq_service.start_consuming(self.queue_name)

            self.is_running = True
            logger.info("Message event listener started successfully")

        except Exception as e:
            logger.error(f"Failed to start message event listener: {str(e)}")
            self.is_running = False
            raise
    
    async def stop(self):
        """
        Stop the message event listener
        """
        if not self.is_running:
            logger.warning("Message event listener is not running")
            return
        
        try:
            logger.info("Stopping message event listener...")
            
            # Stop consuming from the queue
            if self.queue_name in rabbitmq_service.is_consuming:
                rabbitmq_service.is_consuming[self.queue_name] = False
            
            self.is_running = False
            logger.info("Message event listener stopped successfully")

        except Exception as e:
            logger.error(f"Failed to stop message event listener: {str(e)}")
            raise
    
    async def handle_user_message_event(self, payload: Dict[str, Any], message: AbstractIncomingMessage):
        """
        Handle incoming user message events
        
        Args:
            payload: Event payload containing user message and conversation ID
            message: RabbitMQ message object
        """
        try:
            logger.info("🎯 HANDLING USER MESSAGE EVENT")
            logger.info(f"🎯 EVENT HANDLER - Routing Key: {message.routing_key}")
            logger.info(f"🎯 EVENT HANDLER - Exchange: {message.exchange}")

            # Log payload details
            logger.info(f"🎯 EVENT PAYLOAD - Full Payload: {payload}")

            # Extract message data
            user_message = payload.get("message", "")
            conversation_id = payload.get("chatbotConversationId")

            logger.info(f"🎯 EXTRACTED DATA - User Message: '{user_message}'")
            logger.info(f"🎯 EXTRACTED DATA - Conversation ID: {conversation_id}")

            # Allow None/empty messages to pass through for image upload handling
            # The conversation logic will handle these cases appropriately
            if user_message is None:
                logger.info("📸 IMAGE UPLOAD DETECTED - Message is None, will be handled by conversation logic")
            elif not user_message.strip():
                logger.info("📸 EMPTY MESSAGE DETECTED - Message is empty/whitespace, will be handled by conversation logic")

            # Handle new conversation (conversation_id is None)
            if not conversation_id:
                logger.info("ℹ️ NEW CONVERSATION - Message for new conversation should be handled by start conversation API")
                return

            # Process existing conversation
            logger.info(f"🔄 PROCESSING CONVERSATION - ID: {conversation_id}")
            await self._process_conversation_message(conversation_id, user_message)

            logger.info("✅ USER MESSAGE EVENT PROCESSED SUCCESSFULLY")

        except Exception as e:
            logger.error(f"Error processing user message event: {str(e)}", exc_info=True)
            raise
    
    async def _process_conversation_message(self, conversation_id: str, user_message: Optional[str]):
        """
        Process a message for an existing conversation
        
        Args:
            conversation_id: UUID of the conversation
            user_message: Message from the user
        """
        try:
            # Get conversation state from Redis
            redis_service = RedisService()
            
            # Try to acquire a processing lock for this conversation to prevent race conditions
            # Retry up to 5 times with exponential backoff before dropping the message
            lock_key = f"conversation_processing_lock:{conversation_id}"
            lock_acquired = False
            max_retries = 5
            base_delay = 0.5  # Start with 500ms delay
            
            for attempt in range(max_retries):
                lock_acquired = redis_service.acquire_lock(lock_key, timeout=30)  # 30 second timeout
                
                if lock_acquired:
                    logger.info(f"🔒 LOCK ACQUIRED on attempt {attempt + 1} for conversation {conversation_id}")
                    break
                
                # If not the last attempt, wait before retrying
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff: 0.5s, 1s, 2s, 4s
                    logger.info(f"🔄 RETRY {attempt + 1}/{max_retries - 1}: Lock busy for conversation {conversation_id}, waiting {delay}s before retry")
                    await asyncio.sleep(delay)
            
            if not lock_acquired:
                logger.warning(f"⚠️ RACE CONDITION PREVENTED: Could not acquire processing lock for conversation {conversation_id} after {max_retries} attempts")
                logger.warning(f"⚠️ Another message is currently being processed for this conversation")
                logger.warning(f"⚠️ User message '{user_message}' will be dropped to prevent state corruption")
                logger.warning(f"⚠️ This typically happens when user sends multiple messages rapidly")
                return
            
            try:
                state = redis_service.get_conversation_state(conversation_id)
                
                # Update conversation activity timestamp
                redis_service.update_conversation_last_activity(conversation_id)
                
                # Reset idle flags when user sends a message
                if state and 'idle_message_sent' in state:
                    state['idle_message_sent'] = False
                    redis_service.store_conversation_state(conversation_id, state)

                if not state:
                    logger.error(f"Conversation {conversation_id} not found or expired")
                    return

                # Get database session
                db_gen = get_db()
                db: Session = next(db_gen)
                
                try:
                    # Get conversation from database
                    conversation = db.query(ChatbotConversation).filter(
                        ChatbotConversation.id == conversation_id
                    ).first()
                    
                    if not conversation:
                        logger.error(f"Conversation {conversation_id} not found in database")
                        return
                    
                    # Branch by chatbot type with enhanced debugging
                    chatbot_type = state.get("chatbotType")
                    logger.info(f"🔍 CONVERSATION BRANCHING - Chatbot Type: '{chatbot_type}', Conversation ID: {conversation_id}")
                    logger.info(f"🔍 CONVERSATION STATE - Full state keys: {list(state.keys())}")
                    
                    if chatbot_type == "RULE":
                        logger.info(f"🤖 RULE-BASED FLOW - Processing conversation {conversation_id} with RULE logic")
                        await self._continue_rule_based_logic(
                            db, conversation, state, user_message, conversation_id
                        )
                    else:
                        logger.info(f"🧠 AI-BASED FLOW - Processing conversation {conversation_id} with AI logic (chatbotType: '{chatbot_type}')")
                        # Process the conversation using existing AI/knowledgebase logic
                        await self._continue_conversation_logic(
                            db, conversation, state, user_message, conversation_id
                        )
                    
                finally:
                    db.close()
                    
            finally:
                # Release the processing lock (only if it was acquired)
                if lock_acquired:
                    redis_service.release_lock(lock_key)
                    logger.info(f"🔓 RELEASED PROCESSING LOCK for conversation {conversation_id}")

        except Exception as e:
            logger.error(f"Error processing conversation message: {str(e)}")
            raise

    async def _continue_rule_based_logic(
        self,
        db: Session,
        conversation: ChatbotConversation,
        state: Dict[str, Any],
        user_message: Optional[str],
        conversation_id: str
    ):
        """
        Continue a RULE-based chatbot conversation by traversing nodes/edges.
        """
        try:
            from app.models import ChatbotNode, ChatbotEdge
            
            # Initialize entity field service for variable substitution
            entity_field_service = EntityFieldService()
            
            # Ensure current node exists in state (for first rule-based step after start)
            current_node_id = state.get("rule_current_node_id")
            
            # Get field values for variable substitution based on node variable mappings
            field_values = {}
            entity_details = state.get("entity_details", [])
            if entity_details and current_node_id:
                # Get auth token from conversation state
                auth_token = state.get("auth_token") or "dummy_token"
                
                # Get variable mappings from the current node
                current_node = self._load_node(db, state, current_node_id)
                variable_mappings = []
                
                # First try to get from database model (for existing implementation)
                if current_node and hasattr(current_node, 'variable_mapping') and current_node.variable_mapping:
                    variable_mappings = current_node.variable_mapping
                    logger.info(f"Found DB variable mappings: {variable_mappings}")
                    
                # If no DB mappings, try JSON data (for rule-based chatbots)
                elif current_node and hasattr(current_node, 'data') and current_node.data:
                    json_variable_mappings = current_node.data.get("variableMapping") or []
                    if json_variable_mappings:
                        variable_mappings = json_variable_mappings
                        logger.info(f"Found JSON variable mappings: {variable_mappings}")
                
                if variable_mappings:
                    allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id"))
                    field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, variable_mappings, auth_token, allowed_entities)
                    logger.info(f"Retrieved field values from node mappings: {field_values}")
                else:
                    logger.info("No variable mappings found in current node")
            if not current_node_id:
                start_node = self._get_start_node(db, state)
                if not start_node:
                    logger.error("Rule-based flow has no start node")
                    return
                current_node_id = start_node.node_id
                state["rule_current_node_id"] = current_node_id
                RedisService().store_conversation_state(conversation_id, state)

            current_node = self._load_node(db, state, current_node_id)
            if not current_node:
                logger.error(f"Current node {current_node_id} not found")
                return
            
            # Validate that the user's input is valid for the current node
            # This prevents processing stale button clicks after the conversation has moved forward
            node_type = (getattr(current_node, "type", "") or "").strip()
            
            # Check if user is sending a button ID but current node is not a button/question/list node
            if user_message and user_message.startswith("button-"):
                if node_type not in ["buttons", "question", "list"]:
                    logger.warning(f"⚠️ STALE BUTTON CLICK REJECTED: User sent button ID '{user_message}' but current node {current_node_id} is type '{node_type}'")
                    logger.warning(f"⚠️ This typically happens when user taps multiple buttons rapidly and the conversation has already moved forward")
                    logger.warning(f"⚠️ Ignoring this input to prevent conversation state corruption")
                    return
                
                # Additional check: Track rapid button clicks to prevent button ID collisions
                import time
                current_time = time.time()
                last_button_click_time = state.get("last_button_click_time", 0)
                last_button_click_node = state.get("last_button_click_node")
                
                # If a button was clicked less than 5 seconds ago on a DIFFERENT node, reject this click
                if last_button_click_time and (current_time - last_button_click_time) < 5:
                    if last_button_click_node and last_button_click_node != current_node_id:
                        logger.warning(f"⚠️ RAPID BUTTON CLICK REJECTED: '{user_message}' - previous click {current_time - last_button_click_time:.2f}s ago on different node")
                        return
                
                # Update the last button click tracking
                state["last_button_click_time"] = current_time
                state["last_button_click_node"] = current_node_id
                RedisService().store_conversation_state(conversation_id, state)
            
            # MEDIA VALIDATION CHECK - Must be done BEFORE general validation to provide specific error message
            # For question nodes with no options (free text), check if user uploaded media when not allowed
            if node_type == "question":
                try:
                    node_data = current_node.data or {}
                    options = node_data.get("options") or []
                    is_media = node_data.get("isMedia", False)
                    
                    # Only check for free text questions (no options)
                    if not options:
                        # Check if user message is None or empty (indicating media upload without text)
                        is_media_only = not user_message or not user_message.strip() or (user_message and user_message.strip().lower() == 'none')
                        
                        if is_media_only and not is_media:
                            # isMedia=false but user uploaded media - re-ask the question with error
                            logger.info(f"📸 MEDIA NOT ALLOWED - User uploaded media but isMedia=false for question node, re-asking question")
                            await self._re_ask_question_with_media_error(db, state, current_node, conversation_id)
                            return
                except Exception as e:
                    logger.error(f"Error in media validation check: {str(e)}")
            
            if node_type in ["buttons", "question", "list"]:
                # Check if the user's input matches any valid option/button for this node
                logger.info(f"🔍 INPUT VALIDATION - Checking user_message: '{user_message}' for node {current_node_id} ({node_type})")
                is_valid_input = self._is_valid_input_for_node(current_node, user_message)
                logger.info(f"🔍 INPUT VALIDATION RESULT - is_valid_input: {is_valid_input}")
                if not is_valid_input:
                    logger.warning(f"⚠️ INVALID INPUT REJECTED: '{user_message}' not valid for node {current_node_id} ({node_type})")
                    return

            # Capture answer for question nodes and perform per-answer entity update FIRST
            # This ensures answers are captured even when there are no outgoing edges
            try:
                node_type = (getattr(current_node, "type", "") or "").strip()
                skip_entity_update = False  # Flag to skip entity updates for media-only uploads
                
                # Handle entity updates for question, button, and list nodes
                if node_type in ["question", "buttons", "list"]:
                    try:
                        node_data = current_node.data or {}
                    except Exception as e:
                        node_data = {}
                        logger.error(f"Error loading node_data: {e}")

                    # Apply variable substitution to get the correct question text
                    substituted_data = entity_field_service.substitute_node_variables(node_data, field_values)
                    question_text = substituted_data.get("text") or substituted_data.get("body") or ""
                    selected_value = user_message or ""

                    # Handle different node types for option/button selection
                    if node_type == "question":
                        options = node_data.get("options") or []
                        is_media = node_data.get("isMedia", False)
                        
                        # For free text questions (no options), check if we need to skip entity updates
                        if not options:
                            # Check if user message is None or empty (indicating media upload without text)
                            # Also check for string 'None' which is sent when media is uploaded without text
                            is_media_only = not user_message or not user_message.strip() or user_message.strip().lower() == 'none'
                            
                            if is_media_only and is_media:
                                # isMedia=true and content is none/empty - skip entity update and trigger next node
                                logger.info(f"📸 MEDIA UPLOAD DETECTED - isMedia=true, content is empty, skipping entity update and triggering next node")
                                # Skip the entity field update logic by setting flag
                                skip_entity_update = True
                        # Try to resolve the selected option text/name if numeric or exact match
                        try:
                            import re as _re
                            normalized_input = (user_message or "").strip().lower()
                            matched = False
                            # Numeric selection like "1" or "1."
                            m = _re.match(r"\s*(\d+)\.?\s*$", normalized_input)
                            if m and options:
                                idx = int(m.group(1)) - 1
                                if 0 <= idx < len(options):
                                    opt = options[idx]
                                    selected_value = opt.get("text") or opt.get("name") or selected_value
                                    matched = True
                            if not matched and options:
                                for opt in options:
                                    if (opt.get("text") or "").strip().lower() == normalized_input or (opt.get("name") or "").strip().lower() == normalized_input:
                                        selected_value = opt.get("text") or opt.get("name") or selected_value
                                        matched = True
                                        break
                        except Exception:
                            pass
                    elif node_type == "buttons":
                        buttons = node_data.get("buttons") or []
                        # Find the selected button by ID, name, or text
                        for btn in buttons:
                            if (btn.get("id") == user_message or 
                                btn.get("name") == user_message or 
                                self._normalize_text(btn.get("text")) == self._normalize_text(user_message)):
                                selected_value = btn.get("text") or btn.get("name") or selected_value
                                break
                    elif node_type == "list":
                        sections = node_data.get("sections") or []
                        # Find the selected row by ID or text
                        for section in sections:
                            rows = section.get("rows", [])
                            for row in rows:
                                row_id = row.get("id", "")
                                row_text = row.get("text", "")
                                if (row_id == user_message or 
                                    self._normalize_text(row_text) == self._normalize_text(user_message)):
                                    selected_value = row_text or row_id or selected_value
                                    break


                    # Build answer entries based on node entity field mapping
                    # Skip entity updates if this is a media-only upload on a question with isMedia=true
                    if not skip_entity_update:
                        new_answers = []

                        # Prefer DB-backed relationship mapping if available
                        node_entity_fields = []
                        try:
                            node_entity_fields = list(getattr(current_node, "entity_fields", []) or [])
                        except Exception as e:
                            node_entity_fields = []
                            logger.debug(f"No DB entity fields found: {e}")

                        if node_entity_fields:
                            for ef in node_entity_fields:
                                answer_entry = {
                                    "question_id": getattr(current_node, "node_id", None),
                                    "question": question_text,
                                    "answer": selected_value,
                                    "field_name": getattr(ef, "name", None),
                                    "entity_type": getattr(ef, "entity_type", None),
                                    "standard": getattr(ef, "standard", False)  # Include standard flag from DB
                                }
                                new_answers.append(answer_entry)
                        else:
                            # Fallback to JSON mapping in node.data.entityFields
                            json_entity_fields = node_data.get("entityFields") or []
                            
                            if not json_entity_fields and node_type in ["buttons", "question", "list"]:
                                logger.warning(f"⚠️ NO ENTITY FIELDS: Node {getattr(current_node, 'node_id', 'unknown')} has no entity field mappings - entity updates will not be triggered")
                            
                            for ef in json_entity_fields:
                                # Handle both formats: entity_type/name and entityType/displayName
                                field_name = ef.get("name") or ef.get("displayName")
                                entity_type = ef.get("entity_type") or ef.get("entityType")
                                field_id = ef.get("fieldId")
                                is_standard = ef.get("standard", False)  # Get standard flag from entityFields JSON
                                
                                answer_entry = {
                                    "question_id": getattr(current_node, "node_id", None),
                                    "question": question_text,
                                    "answer": selected_value,
                                    "field_name": field_name,
                                    "entity_type": entity_type,
                                    "field_id": field_id,
                                    "standard": is_standard  # Pass the standard flag
                                }
                                new_answers.append(answer_entry)

                        if new_answers:
                            state.setdefault("answers", []).extend(new_answers)
                            # Per-answer entity update for RULE flow
                            try:
                                entity_details = state.get("entity_details", [])
                                
                                if entity_details:
                                    cs = ChatbotService()
                                    # Ensure numeric user_id for event metadata
                                    user_id_safe = state.get("user_id")
                                    try:
                                        user_id_safe = int(user_id_safe)
                                    except Exception as e:
                                        user_id_safe = 0
                                        logger.warning(f"⚠️ Failed to convert user_id to int: {e}, using 0")
                                    
                                    update_result = await cs.update_entities_after_conversation(
                                        entity_details,
                                        new_answers,
                                        int(state.get("tenant_id") or 0),
                                        user_id_safe,
                                        state.get("chatbot_id"),
                                        state
                                    )
                                    logger.info(f"✅ Entity update completed: {len(update_result.get('successful_updates', []))} successful")
                                else:
                                    logger.warning("⚠️ Skipping entity update: no entity_details")
                            except Exception as e:
                                logger.error(f"❌ Entity update failed: {str(e)}")
                    else:
                        logger.info(f"⏭️ Skipping entity update for media-only upload (isMedia=true, no text content)")
            except Exception as e:
                logger.error(f"Error capturing rule-based answer or updating entity: {str(e)}")

            # Check if this is a question or list node with invalid option selection
            node_type = (getattr(current_node, "type", "") or "").strip()
            logger.info(f"🔍 MAIN FLOW DEBUG - Node type: {node_type}, user_message: '{user_message}'")
            if node_type in ["question", "list"]:
                is_valid = self._is_valid_option_input(current_node, user_message)
                logger.info(f"🔍 MAIN FLOW DEBUG - _is_valid_option_input returned: {is_valid}")
                if not is_valid:
                    logger.info(f"❌ INVALID OPTION - User provided '{user_message}' which is not a valid option - RE-ASKING QUESTION")
                    # Re-ask the question with an error message
                    await self._re_ask_question_with_error(db, state, current_node, conversation_id, user_message)
                    return
                else:
                    logger.info(f"✅ VALID INPUT - Proceeding to edge selection")
                
            # Select next edge based on user input and handlers
            outgoing = self._get_outgoing_edges(db, state, current_node.node_id)
            logger.info(f"🔍 MAIN FLOW DEBUG - Found {len(outgoing)} outgoing edges for node {current_node.node_id}")
            next_edge = self._select_edge_for_input(current_node, outgoing, user_message)
            logger.info(f"🔍 MAIN FLOW DEBUG - _select_edge_for_input returned: {next_edge}")
            if not next_edge:
                logger.info(f"🔍 MAIN FLOW DEBUG - No edge found, entering terminal handling logic")
                # Check if current node is a question, list, or button node without outgoing edges
                # Note: If we reach here for a question/list/button node, it means either:
                # 1. The node has no outgoing edges (terminal node)
                # 2. The user selected a valid option/button but that specific option/button has no outgoing edge configured
                node_type = (getattr(current_node, "type", "") or "").strip()
                logger.info(f"🔍 TERMINAL HANDLING DEBUG - Node type: {node_type}, checking if in ['question', 'list', 'buttons']")
                if node_type in ["question", "list", "buttons"]:
                    # User selected a valid option (already validated above), but no edge found
                    # This is a valid scenario - the selected option is a terminal choice
                    logger.info(f"✅ TERMINAL HANDLING - Node type {node_type} qualifies for terminal handling")
                    # Get the question data to provide a meaningful response
                    try:
                        node_data = current_node.data or {}
                    except Exception:
                        node_data = {}
                    
                    # Apply variable substitution to get the correct node text
                    substituted_data = entity_field_service.substitute_node_variables(node_data, field_values)
                    
                    # Try to find the selected option to acknowledge the user's choice
                    selected_option_text = user_message
                    
                    if node_type == "question":
                        question_text = substituted_data.get("text") or ""
                        options = node_data.get("options") or []
                        logger.info(f"🔍 TERMINAL QUESTION DEBUG - Question text: '{question_text}', options count: {len(options)}")

                        if options:
                            # Try to match by numeric input (1, 2, 3) or exact text
                            try:
                                if user_message.isdigit():
                                    idx = int(user_message) - 1
                                    if 0 <= idx < len(options):
                                        selected_option_text = options[idx].get("text", user_message)
                            except Exception:
                                pass
                            
                            # Try direct text match
                            for opt in options:
                                if (opt.get("text", "").lower() == user_message.lower() or 
                                    opt.get("name", "").lower() == user_message.lower()):
                                    selected_option_text = opt.get("text", user_message)
                                    break
                        
                        # Create acknowledgment message
                        acknowledgment_message = f"Thank you for your response: {selected_option_text}"
                        if question_text:
                            acknowledgment_message = f"Thank you for answering '{question_text}' with: {selected_option_text}"
                        logger.info(f"🔍 TERMINAL QUESTION DEBUG - Created acknowledgment: '{acknowledgment_message}'")
                        
                        # Handle entity updates for question nodes (similar to button/list nodes)
                        json_entity_fields = node_data.get("entityFields") or []
                        if json_entity_fields:
                            new_answers = []
                            for ef in json_entity_fields:
                                # Handle both formats: entity_type/name and entityType/displayName
                                field_name = ef.get("name") or ef.get("displayName")
                                entity_type = ef.get("entity_type") or ef.get("entityType")
                                field_id = ef.get("fieldId")
                                is_standard = ef.get("standard", False)
                                
                                answer_entry = {
                                    "question_id": getattr(current_node, "node_id", None),
                                    "question": question_text,
                                    "answer": selected_option_text,
                                    "field_name": field_name,
                                    "entity_type": entity_type,
                                    "field_id": field_id,
                                    "standard": is_standard
                                }
                                new_answers.append(answer_entry)
                            
                            if new_answers:
                                state.setdefault("answers", []).extend(new_answers)
                                # Per-answer entity update for question nodes
                                try:
                                    entity_details = state.get("entity_details", [])
                                    if entity_details:
                                        cs = ChatbotService()
                                        user_id_safe = state.get("user_id")
                                        try:
                                            user_id_safe = int(user_id_safe)
                                        except Exception as e:
                                            user_id_safe = 0
                                            logger.warning(f"⚠️ Failed to convert user_id: {e}")
                                        
                                        _ = await cs.update_entities_after_conversation(
                                            entity_details,
                                            new_answers,
                                            int(state.get("tenant_id")),
                                            user_id_safe,
                                            state.get("chatbot_id"),
                                            state
                                        )
                                        logger.info("✅ Question entity update completed")
                                except Exception as e:
                                    logger.error(f"❌ Question entity update failed: {str(e)}")
                            
                    elif node_type == "list":
                        header = substituted_data.get("header") or ""
                        sections = node_data.get("sections") or []
                        
                        # Try to find the selected row in the list
                        for section in sections:
                            rows = section.get("rows", [])
                            for row in rows:
                                row_id = row.get("id", "")
                                row_text = row.get("text", "")
                                
                                if (row_id.lower() == user_message.lower() or
                                    row_text.lower() == user_message.lower()):
                                    selected_option_text = row_text or row_id
                                    break
                        
                        # Check numeric selection
                        try:
                            if user_message.isdigit():
                                total_rows = sum(len(section.get("rows", [])) for section in sections)
                                idx = int(user_message) - 1
                                if 0 <= idx < total_rows:
                                    # Find the row at this index
                                    current_idx = 0
                                    for section in sections:
                                        rows = section.get("rows", [])
                                        for row in rows:
                                            if current_idx == idx:
                                                selected_option_text = row.get("text") or row.get("id", "")
                                                break
                                            current_idx += 1
                                        if current_idx > idx:
                                            break
                        except Exception:
                            pass
                        
                        # Create acknowledgment message
                        acknowledgment_message = f"Thank you for your selection: {selected_option_text}"
                        if header:
                            acknowledgment_message = f"Thank you for selecting from '{header}': {selected_option_text}"
                        
                        # Handle entity updates for list nodes (similar to button nodes)
                        json_entity_fields = node_data.get("entityFields") or []
                        if json_entity_fields:
                            new_answers = []
                            for ef in json_entity_fields:
                                # Handle both formats: entity_type/name and entityType/displayName
                                field_name = ef.get("name") or ef.get("displayName")
                                entity_type = ef.get("entity_type") or ef.get("entityType")
                                field_id = ef.get("fieldId")
                                is_standard = ef.get("standard", False)
                                
                                answer_entry = {
                                    "question_id": getattr(current_node, "node_id", None),
                                    "question": f"List selection: {selected_option_text}",
                                    "answer": selected_option_text,
                                    "field_name": field_name,
                                    "entity_type": entity_type,
                                    "field_id": field_id,
                                    "standard": is_standard
                                }
                                new_answers.append(answer_entry)
                            
                            if new_answers:
                                state.setdefault("answers", []).extend(new_answers)
                                # Per-answer entity update for list nodes
                                try:
                                    entity_details = state.get("entity_details", [])
                                    if entity_details:
                                        cs = ChatbotService()
                                        user_id_safe = state.get("user_id")
                                        try:
                                            user_id_safe = int(user_id_safe)
                                        except Exception as e:
                                            user_id_safe = 0
                                            logger.warning(f"⚠️ Failed to convert user_id: {e}")
                                        
                                        _ = await cs.update_entities_after_conversation(
                                            entity_details,
                                            new_answers,
                                            int(state.get("tenant_id")),
                                            user_id_safe,
                                            state.get("chatbot_id"),
                                            state
                                        )
                                        logger.info("✅ List entity update completed")
                                except Exception as e:
                                    logger.error(f"❌ List entity update failed: {str(e)}")

                    elif node_type == "buttons":
                        # Handle button nodes without outgoing edges
                        buttons = node_data.get("buttons") or []

                        # Try to find the selected button
                        selected_button_text = user_message
                        for btn in buttons:
                            if (btn.get("id") == user_message or
                                btn.get("name") == user_message or
                                btn.get("text", "").lower() == user_message.lower()):
                                selected_button_text = btn.get("text", user_message)
                                break

                        # Create acknowledgment message
                        acknowledgment_message = f"Thank you for your selection: {selected_button_text}"

                        # Handle entity updates for button nodes
                        json_entity_fields = node_data.get("entityFields") or []
                        if json_entity_fields:
                            new_answers = []
                            for ef in json_entity_fields:
                                # Handle both formats: entity_type/name and entityType/displayName
                                field_name = ef.get("name") or ef.get("displayName")
                                entity_type = ef.get("entity_type") or ef.get("entityType")
                                field_id = ef.get("fieldId")
                                is_standard = ef.get("standard", False)

                                answer_entry = {
                                    "question_id": getattr(current_node, "node_id", None),
                                    "question": f"Button selection: {selected_button_text}",
                                    "answer": selected_button_text,
                                    "field_name": field_name,
                                    "entity_type": entity_type,
                                    "field_id": field_id,
                                    "standard": is_standard
                                }
                                new_answers.append(answer_entry)

                            if new_answers:
                                state.setdefault("answers", []).extend(new_answers)
                                # Per-answer entity update for button nodes
                                try:
                                    entity_details = state.get("entity_details", [])
                                    if entity_details:
                                        cs = ChatbotService()
                                        user_id_safe = state.get("user_id")
                                        try:
                                            user_id_safe = int(user_id_safe)
                                        except Exception as e:
                                            user_id_safe = 0
                                            logger.warning(f"⚠️ Failed to convert user_id: {e}")

                                        _ = await cs.update_entities_after_conversation(
                                            entity_details,
                                            new_answers,
                                            int(state.get("tenant_id")),
                                            user_id_safe,
                                            state.get("chatbot_id"),
                                            state
                                        )
                                        logger.info("✅ Button entity update completed")
                                except Exception as e:
                                    logger.error(f"❌ Button entity update failed: {str(e)}")

                    logger.info(f"Terminal option/button selected - ending conversation with acknowledgment")
                    logger.info(f"🏁 TERMINAL OPTION REACHED - Node {current_node_id} ({node_type}) option has no outgoing edge, publishing completion event")
                    
                    # Mark conversation as completed in state and database
                    state["completed"] = True
                    state["ended"] = True
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)
                    
                    # Update conversation in database to mark as completed
                    update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
                    logger.info(f"💾 DATABASE UPDATED - Conversation {conversation_id} marked as completed=True, ended=True")
                    
                    # For rule-based chatbots, message should be null (content is in nodeDetails)
                    completion_message = None
                    
                    # Log entity details for terminal option
                    entity_details = state.get("entity_details", [])
                    logger.info(f"📊 TERMINAL OPTION ENTITY DETAILS - Count: {len(entity_details)}")
                    
                    # Normalize and enrich entity details to ensure correct format
                    auth_token = state.get("auth_token") or "dummy_token"
                    normalized_entity_details = []
                    
                    for i, entity in enumerate(entity_details, 1):
                        # Support both old and new formats
                        entity_id = entity.get('id') or entity.get('entityId')
                        entity_type = entity.get('entityType') or entity.get('entity')
                        owner_id = entity.get('ownerId')
                        entity_name = entity.get('entityName') or entity.get('name')
                        
                        # Normalize entity type to uppercase
                        if entity_type:
                            entity_type = entity_type.upper()
                        
                            # If ownerId or entityName is missing, fetch from API
                            if (not owner_id or not entity_name) and entity_id and entity_type:
                                logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                try:
                                    entity_field_service = EntityFieldService()
                                    entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                    fetched_owner_id = entity_info.get("ownerId")
                                    fetched_entity_name = entity_info.get("entityName")
                                    
                                    if fetched_owner_id:
                                        owner_id = fetched_owner_id
                                        logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                    else:
                                        logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                        # Keep existing owner_id if API call failed
                                    
                                    if fetched_entity_name:
                                        entity_name = fetched_entity_name
                                        logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                    else:
                                        logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                        # Keep existing entity_name if API call failed
                                        
                                except Exception as e:
                                    logger.error(f"❌ Error fetching entity details for entity {i}: {str(e)}")
                                    logger.warning(f"⚠️ API ENRICHMENT FAILED - Entity {i}: Keeping existing values (ownerId={owner_id}, entityName={entity_name})")
                        # Always use the new format
                        normalized_entity = {
                            "entityId": int(entity_id) if entity_id else None,
                            "entityType": entity_type,
                            "ownerId": int(owner_id) if owner_id else None,
                            "entityName": entity_name
                        }
                        normalized_entity_details.append(normalized_entity)
                        logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")
                    
                    # Publish completion event with entity details
                    logger.info(f"🔍 COMPLETION EVENT DEBUG - About to publish completion event")
                    logger.info(f"🔍 COMPLETION EVENT DEBUG - conversation_id: {conversation_id}")
                    logger.info(f"🔍 COMPLETION EVENT DEBUG - completion_message: {completion_message}")
                    logger.info(f"🔍 COMPLETION EVENT DEBUG - tenant_id: {state.get('tenant_id')}")
                    logger.info(f"🔍 COMPLETION EVENT DEBUG - entity_details count: {len(normalized_entity_details)}")

                    await conversation_event_publisher.publish_conversation_completion(
                        chatbot_conversation_id=conversation_id,
                        completion_message=completion_message,
                        charge=0,
                        chatbot_type="RULE",
                        tenant_id=state.get("tenant_id"),
                        entity_details=normalized_entity_details,  # Use normalized format
                        node_details=None  # No node details for terminal options
                    )

                    logger.info(f"✅ COMPLETION EVENT PUBLISHED (TERMINAL OPTION) - For node {current_node_id} with acknowledgment: {acknowledgment_message}")
                    
                    # Clear Redis state after successful completion
                    redis_service.clear_conversation_state(conversation_id)
                    logger.info(f"🧹 REDIS STATE CLEARED - Conversation {conversation_id} completed")
                    
                    return
                else:
                    logger.info(f"🔍 TERMINAL HANDLING DEBUG - Node type {node_type} NOT in ['question', 'list', 'buttons'], using fallback terminal handling")
                    logger.info("No outgoing edge found; ending rule-based conversation")
                    logger.info(f"🏁 TERMINAL NODE REACHED - Node {current_node_id} has no outgoing edges, publishing completion event")
                    
                    # Mark conversation as completed in state and database
                    state["completed"] = True
                    state["ended"] = True
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)
                    
                    # Update conversation in database to mark as completed
                    update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
                    logger.info(f"💾 DATABASE UPDATED - Conversation {conversation_id} marked as completed=True, ended=True")
                    
                    # For rule-based chatbots, message should be null (content is in nodeDetails)
                    completion_message = None
                    
                    # Log entity details for terminal node
                    entity_details = state.get("entity_details", [])
                    logger.info(f"📊 TERMINAL NODE ENTITY DETAILS - Count: {len(entity_details)}")
                    
                    # Normalize and enrich entity details to ensure correct format
                    auth_token = state.get("auth_token") or "dummy_token"
                    normalized_entity_details = []
                    
                    for i, entity in enumerate(entity_details, 1):
                        # Support both old and new formats
                        entity_id = entity.get('id') or entity.get('entityId')
                        entity_type = entity.get('entityType') or entity.get('entity')
                        owner_id = entity.get('ownerId')
                        entity_name = entity.get('entityName') or entity.get('name')
                        
                        # Normalize entity type to uppercase
                        if entity_type:
                            entity_type = entity_type.upper()
                        
                            # If ownerId or entityName is missing, fetch from API
                            if (not owner_id or not entity_name) and entity_id and entity_type:
                                logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                try:
                                    entity_field_service = EntityFieldService()
                                    entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                    fetched_owner_id = entity_info.get("ownerId")
                                    fetched_entity_name = entity_info.get("entityName")
                                    
                                    if fetched_owner_id:
                                        owner_id = fetched_owner_id
                                        logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                    else:
                                        logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                        # Keep existing owner_id if API call failed
                                    
                                    if fetched_entity_name:
                                        entity_name = fetched_entity_name
                                        logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                    else:
                                        logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                        # Keep existing entity_name if API call failed
                                        
                                except Exception as e:
                                    logger.error(f"❌ Error fetching entity details for entity {i}: {str(e)}")
                                    logger.warning(f"⚠️ API ENRICHMENT FAILED - Entity {i}: Keeping existing values (ownerId={owner_id}, entityName={entity_name})")
                        # Always use the new format
                        normalized_entity = {
                            "entityId": int(entity_id) if entity_id else None,
                            "entityType": entity_type,
                            "ownerId": int(owner_id) if owner_id else None,
                            "entityName": entity_name
                        }
                        normalized_entity_details.append(normalized_entity)
                        logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")
                    
                    # Publish completion event with entity details
                    await conversation_event_publisher.publish_conversation_completion(
                        chatbot_conversation_id=conversation_id,
                        completion_message=completion_message,
                        charge=0,
                        chatbot_type="RULE",
                        tenant_id=state.get("tenant_id"),
                        entity_details=normalized_entity_details,  # Use normalized format
                        node_details=None  # No node details for non-interactive terminal nodes
                    )
                    
                    logger.info(f"✅ COMPLETION EVENT PUBLISHED (TERMINAL NODE) - For node {current_node_id}")
                    
                    # Clear Redis state after successful completion
                    redis_service.clear_conversation_state(conversation_id)
                    logger.info(f"🧹 REDIS STATE CLEARED - Conversation {conversation_id} completed")
                    
                    return

            # Load next node
            next_node = self._load_node(db, state, next_edge.target_node)
            if not next_node:
                logger.error(f"Target node {next_edge.target_node} not found")
                return

            # Build message for next node and publish
            next_message = self._build_message_for_node(next_node, {})

            # If next node is a pure condition, skip to its next until message exists or no edges
            guard_limit = 5
            while (not next_message) and guard_limit > 0:
                guard_limit -= 1
                outgoing2 = self._get_outgoing_edges(db, state, next_node.node_id)
                if not outgoing2:
                    break
                next_node = self._load_node(db, state, outgoing2[0].target_node)
                if not next_node:
                    break
                next_message = self._build_message_for_node(next_node, {})

            # Update current node in state
            state["rule_current_node_id"] = next_node.node_id
            state.setdefault("history", []).append({"role": "user", "content": user_message or ""})
            if next_message:
                state["history"].append({"role": "assistant", "content": next_message})
            RedisService().store_conversation_state(conversation_id, state)
            update_conversation_in_db(db, conversation_id, state)

            # Build nodeDetails for all node types
            node_details = None
            node_type = (getattr(next_node, "type", "") or "").strip()
            
            if node_type == "sendMessage":
                try:
                    # Use stored isFirstNode value from database (much more efficient)
                    is_first_node = getattr(next_node, 'is_first_node', False)
                    
                    node_details = {
                        "id": next_node.node_id,  # Keep as string to maintain consistency
                        "name": getattr(next_node, "name", None),
                        "type": "sendMessage",
                        "isFirstNode": is_first_node
                    }
                    data_obj = (getattr(next_node, "data", None) or {})
                    
                    # Get variable mappings from next node and merge with current field values
                    # Get variable mappings from next node
                    next_node_mappings = []
                    # Check multiple possible locations for variable mappings
                    if hasattr(next_node, 'variable_mapping') and next_node.variable_mapping:
                        next_node_mappings = next_node.variable_mapping
                    elif hasattr(next_node, 'variableMapping') and next_node.variableMapping:
                        next_node_mappings = next_node.variableMapping
                    elif hasattr(next_node, 'data') and next_node.data:
                        next_node_mappings = next_node.data.get("variableMapping") or []
                    
                    logger.info(f"🔍 DEBUG: Found next_node_mappings for sendMessage: {next_node_mappings}")
                    
                    # If node has mappings, use those; otherwise use empty dict
                    if next_node_mappings:
                        # Get auth token from state for variable mapping
                        auth_token = state.get("auth_token") or "dummy_token"
                        allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id")) if db else None
                        node_field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, next_node_mappings, auth_token, allowed_entities)
                        logger.info(f"Retrieved field values from next sendMessage node mappings: {node_field_values}")
                    else:
                        node_field_values = {}
                    
                    # Apply variable substitution to node data
                    substituted_data = entity_field_service.substitute_node_variables(data_obj, node_field_values)
                    
                    blocks = []
                    for opt in (substituted_data.get("options") or []):
                        if opt.get("type") == "text":
                            blocks.append({"type": "text", "text": opt.get("text")})
                        elif opt.get("type") == "media":
                            blocks.append({"type": "media", "mediaFile": opt.get("mediaFile")})
                    node_details["data"] = blocks
                    
                    logger.info(f"🏁 NODE DETAILS - Built for sendMessage node {next_node.node_id} with stored isFirstNode: {is_first_node}")
                except Exception as e:
                    logger.error(f"Error building nodeDetails for sendMessage with variable substitution: {str(e)}")
                    node_details = None
                    
            elif node_type == "question":
                try:
                    # Use stored isFirstNode value from database
                    is_first_node = getattr(next_node, 'is_first_node', False)
                    
                    node_details = {
                        "id": next_node.node_id,
                        "name": getattr(next_node, "name", None),
                        "type": "question",
                        "isFirstNode": is_first_node
                    }
                    data_obj = (getattr(next_node, "data", None) or {})
                    
                    # Get variable mappings from next node
                    next_node_mappings = []
                    # Check multiple possible locations for variable mappings
                    if hasattr(next_node, 'variable_mapping') and next_node.variable_mapping:
                        next_node_mappings = next_node.variable_mapping
                    elif hasattr(next_node, 'variableMapping') and next_node.variableMapping:
                        next_node_mappings = next_node.variableMapping
                    elif hasattr(next_node, 'data') and next_node.data:
                        next_node_mappings = next_node.data.get("variableMapping") or []
                    
                    logger.info(f"🔍 DEBUG: Found next_node_mappings: {next_node_mappings}")
                    
                    # If node has mappings, use those; otherwise use empty dict (no inheritance)
                    if next_node_mappings:
                        # Get auth token from state for variable mapping
                        auth_token = state.get("auth_token") or "dummy_token"
                        allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id")) if db else None
                        node_field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, next_node_mappings, auth_token, allowed_entities)
                        logger.info(f"Retrieved field values from next question node mappings: {node_field_values}")
                    else:
                        node_field_values = {}
                    
                    # Apply variable substitution to node data
                    substituted_data = entity_field_service.substitute_node_variables(data_obj, node_field_values)
                    
                    question_text = substituted_data.get("text") or ""
                    options = substituted_data.get("options") or []
                    
                    # Format options with newlines for display
                    formatted_options = []
                    for idx, opt in enumerate(options, 1):
                        label = opt.get("text", f"Option {idx}")
                        formatted_options.append({
                            "text": label,
                            "name": opt.get("name"),
                            "position": opt.get("position", idx - 1)
                        })
                    
                    # Create formatted question text with numbered options
                    formatted_question_text = question_text
                    if options:
                        option_lines = []
                        for idx, opt in enumerate(options, 1):
                            option_text = opt.get("text", "")
                            if option_text:
                                option_lines.append(f"{idx}. {option_text}")
                        if option_lines:
                            formatted_question_text = f"{question_text}\n" + "\n".join(option_lines)
                    
                    node_details["data"] = {
                        "text": formatted_question_text,
                        "options": formatted_options
                    }
                    
                    logger.info(f"🏁 NODE DETAILS - Built for question node {next_node.node_id} with stored isFirstNode: {is_first_node}")
                except Exception as e:
                    logger.error(f"Error building nodeDetails for question with variable substitution: {str(e)}")
                    node_details = None
                    
            elif node_type == "buttons":
                try:
                    # Use stored isFirstNode value from database
                    is_first_node = getattr(next_node, 'is_first_node', False)
                    
                    node_details = {
                        "id": next_node.node_id,
                        "name": getattr(next_node, "name", None),
                        "type": "buttons",
                        "isFirstNode": is_first_node
                    }
                    data_obj = (getattr(next_node, "data", None) or {})
                    
                    # Get variable mappings from next node
                    next_node_mappings = []
                    # Check multiple possible locations for variable mappings
                    if hasattr(next_node, 'variable_mapping') and next_node.variable_mapping:
                        next_node_mappings = next_node.variable_mapping
                    elif hasattr(next_node, 'variableMapping') and next_node.variableMapping:
                        next_node_mappings = next_node.variableMapping
                    elif hasattr(next_node, 'data') and next_node.data:
                        next_node_mappings = next_node.data.get("variableMapping") or []
                    
                    logger.info(f"🔍 DEBUG: Found next_node_mappings for button: {next_node_mappings}")
                    
                    # If node has mappings, use those; otherwise use empty dict (no inheritance)
                    if next_node_mappings:
                        # Get field values for this specific node
                        auth_token = state.get("auth_token") or "dummy_token"
                        allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id")) if db else None
                        node_field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, next_node_mappings, auth_token, allowed_entities)
                        logger.info(f"🔍 DEBUG: node_field_values for button: {node_field_values}")
                    else:
                        node_field_values = {}
                    
                    # Apply variable substitution to button data
                    substituted_data = entity_field_service.substitute_node_variables(data_obj, node_field_values)
                    
                    # Build button data structure
                    # Header should be an object with format, text, and mediaFile
                    header = substituted_data.get("header")
                    if isinstance(header, str):
                        # Convert string header to object format
                        header = {
                            "format": "text",
                            "text": header,
                            "mediaFile": None
                        }
                    elif not header:
                        header = {
                            "format": "text",
                            "text": "",
                            "mediaFile": None
                        }
                    elif isinstance(header, dict):
                        # Ensure mediaFile is present (can be None)
                        if "mediaFile" not in header:
                            header["mediaFile"] = None
                    
                    body = substituted_data.get("body") or ""
                    footer = substituted_data.get("footer") or ""
                    
                    # Buttons must have id, name, text, and position fields
                    buttons = substituted_data.get("buttons") or []
                    formatted_buttons = []
                    for btn in buttons:
                        # Ensure we have both id and name for proper click handling
                        # Frontend will send back the 'id' field when button is clicked
                        button_id = btn.get("id") or btn.get("name") or btn.get("text", "")
                        formatted_btn = {
                            "id": button_id,  # Primary field - what frontend sends back
                            "name": button_id,  # Secondary field for compatibility
                            "text": btn.get("text", ""),
                            "position": btn.get("position", 0)
                        }
                        formatted_buttons.append(formatted_btn)
                    
                    node_details["data"] = {
                        "header": header,
                        "body": body,
                        "footer": footer,
                        "buttons": formatted_buttons
                    }
                    
                    logger.info(f"🏁 NODE DETAILS - Built for button node {next_node.node_id} with stored isFirstNode: {is_first_node}")
                    logger.info(f"🏁 NODE DETAILS - Complete node_details: {node_details}")
                    logger.info(f"🏁 NODE DETAILS - Data field present: {'data' in node_details}")
                    if 'data' in node_details:
                        logger.info(f"🏁 NODE DETAILS - Data content: {node_details['data']}")
                    
                    # Handle entity updates for button nodes (similar to question nodes)
                    # Button nodes can have entityFields that need to be updated when user clicks a button
                    node_data = data_obj
                    button_clicked = user_message or ""
                    
                    # Find the button text based on the clicked button ID
                    button_text = button_clicked  # Default to button ID if text not found
                    for btn in formatted_buttons:
                        if btn.get("id") == button_clicked:
                            button_text = btn.get("text", button_clicked)
                            logger.info(f"🔍 BUTTON DEBUG: Found button text '{button_text}' for clicked button ID '{button_clicked}'")
                            break
                    
                    # Check if this button node has entityFields that need updating
                    json_entity_fields = node_data.get("entityFields") or []
                    if json_entity_fields:
                        logger.info(f"🔍 BUTTON DEBUG: Processing entityFields for button node: {json_entity_fields}")
                        
                        new_answers = []
                        for ef in json_entity_fields:
                            # Handle both formats: entity_type/name and entityType/displayName
                            field_name = ef.get("name") or ef.get("displayName")
                            entity_type = ef.get("entity_type") or ef.get("entityType")
                            field_id = ef.get("fieldId")
                            
                            answer_entry = {
                                "question_id": getattr(next_node, "node_id", None),
                                "question": f"Button clicked: {button_clicked}",
                                "answer": button_text,  # Use button text instead of button ID
                                "field_name": field_name,
                                "entity_type": entity_type,
                                "field_id": field_id
                            }
                            new_answers.append(answer_entry)
                        
                        if new_answers:
                            state.setdefault("answers", []).extend(new_answers)
                            # Per-answer entity update for button nodes
                            try:
                                entity_details = state.get("entity_details", [])
                                logger.info(f"🔍 BUTTON DEBUG: entity_details count: {len(entity_details) if entity_details else 0}")
                                logger.info(f"🔍 BUTTON DEBUG: new_answers count: {len(new_answers) if new_answers else 0}")
                                logger.info(f"🔍 BUTTON DEBUG: new_answers content: {new_answers}")
                                
                                if entity_details:
                                    logger.info("🔄 BUTTON: Triggering per-answer entity update")
                                    logger.info(f"🔍 BUTTON DEBUG: tenant_id: {state.get('tenant_id')}")
                                    logger.info(f"🔍 BUTTON DEBUG: chatbot_id: {state.get('chatbot_id')}")
                                    logger.info(f"🔍 BUTTON DEBUG: user_id (raw): {state.get('user_id')}")
                                    
                                    cs = ChatbotService()
                                    # Ensure numeric user_id for event metadata
                                    user_id_safe = state.get("user_id")
                                    try:
                                        user_id_safe = int(user_id_safe)
                                        logger.info(f"🔍 BUTTON DEBUG: user_id (converted): {user_id_safe}")
                                    except Exception as e:
                                        user_id_safe = 0
                                        logger.warning(f"⚠️ BUTTON: Failed to convert user_id to int: {e}, using 0")
                                    
                                    logger.info(f"🔍 BUTTON DEBUG: Calling update_entities_after_conversation with {len(new_answers)} answers")
                                    _ = await cs.update_entities_after_conversation(
                                        entity_details,
                                        new_answers,
                                        int(state.get("tenant_id")),
                                        user_id_safe,
                                        state.get("chatbot_id"),
                                        state
                                    )
                                    logger.info("✅ BUTTON: Entity update completed successfully")
                                else:
                                    logger.warning("⚠️ BUTTON: No entity_details found for button node entity update")
                            except Exception as e:
                                logger.error(f"❌ BUTTON: Entity update failed: {str(e)}")
                                import traceback
                                logger.error(f"❌ BUTTON: Entity update traceback: {traceback.format_exc()}")
                except Exception as e:
                    logger.error(f"Error building nodeDetails for button with variable substitution: {str(e)}")
                    logger.error(f"❌ BUTTON NODE BUILDING FAILED - Node {next_node.node_id}, Error: {str(e)}")
                    import traceback
                    logger.error(f"❌ BUTTON NODE BUILDING TRACEBACK: {traceback.format_exc()}")
                    node_details = None

            elif node_type == "list":
                try:
                    # Use stored isFirstNode value from database
                    is_first_node = getattr(next_node, 'is_first_node', False)
                    
                    node_details = {
                        "id": next_node.node_id,
                        "name": getattr(next_node, "name", None),
                        "type": "list",
                        "isFirstNode": is_first_node
                    }
                    data_obj = (getattr(next_node, "data", None) or {})
                    
                    # Get variable mappings from next node
                    next_node_mappings = []
                    # Check multiple possible locations for variable mappings
                    if hasattr(next_node, 'variable_mapping') and next_node.variable_mapping:
                        next_node_mappings = next_node.variable_mapping
                    elif hasattr(next_node, 'variableMapping') and next_node.variableMapping:
                        next_node_mappings = next_node.variableMapping
                    elif hasattr(next_node, 'data') and next_node.data:
                        next_node_mappings = next_node.data.get("variableMapping") or []
                    
                    logger.info(f"🔍 DEBUG: Found next_node_mappings for list: {next_node_mappings}")
                    
                    # If node has mappings, use those; otherwise use empty dict (no inheritance)
                    if next_node_mappings:
                        # Get auth token from state for variable mapping
                        auth_token = state.get("auth_token") or "dummy_token"
                        allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id")) if db else None
                        node_field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, next_node_mappings, auth_token, allowed_entities)
                        logger.info(f"Retrieved field values from next list node mappings: {node_field_values}")
                    else:
                        node_field_values = {}
                    
                    # Apply variable substitution to list data
                    substituted_data = entity_field_service.substitute_node_variables(data_obj, node_field_values)
                    
                    # Build list data structure
                    header = substituted_data.get("header")
                    if isinstance(header, str):
                        header = {"format": "text", "text": header, "mediaFile": None}
                    
                    body = substituted_data.get("body") or ""
                    footer = substituted_data.get("footer") or ""
                    menu_button = substituted_data.get("menuButton") or "Menu"
                    sections = substituted_data.get("sections") or []
                    
                    node_details["data"] = {
                        "header": header,
                        "body": body,
                        "footer": footer,
                        "menuButton": menu_button,
                        "sections": sections
                    }
                    
                    logger.info(f"🏁 NODE DETAILS - Built for list node {next_node.node_id} with stored isFirstNode: {is_first_node}")
                except Exception as e:
                    logger.error(f"Error building nodeDetails for list with variable substitution: {str(e)}")
                    node_details = None

            # For sendMessage, align with RULE contract: message=None, use nodeDetails
            is_send_message = (getattr(next_node, "type", "") or "").strip() == "sendMessage"
            logger.info(f"🔍 SENDMESSAGE DETECTION - Node {next_node.node_id} type: '{getattr(next_node, 'type', '')}', is_send_message: {is_send_message}")
            
            # Check if this is the last node (no outgoing edges)
            outgoing_edges = self._get_outgoing_edges(db, state, next_node.node_id)
            is_last_node = len(outgoing_edges) == 0
            
            logger.info(f"🔍 NODE COMPLETION CHECK - Node {next_node.node_id} has {len(outgoing_edges)} outgoing edges, is_last_node: {is_last_node}")
            
            # Check for sendMessage chaining: if current node is sendMessage, check if next node is also sendMessage
            # This feature allows automatic chaining of sendMessage nodes without requiring user input
            # Example: sendMessage -> sendMessage -> sendMessage will all be published automatically
            if is_send_message:
                logger.info(f"🔗 SENDMESSAGE CHAINING - Current node {next_node.node_id} is sendMessage, checking for chaining")
                chained_nodes, chain_ends_at_final_node = await self._collect_chained_nodes(db, state, next_node, {}, entity_field_service)
                logger.info(f"🔗 SENDMESSAGE CHAINING - Collected {len(chained_nodes)} chained nodes, chain_ends_at_final_node: {chain_ends_at_final_node}")
                
                # Create a single event with all nodes (current + chained)
                all_nodes = [node_details] if node_details else []
                if chained_nodes:
                    all_nodes.extend(chained_nodes)
                    
                    # Update conversation state to point to the last node if it's a question or buttons node
                    # This ensures that subsequent user input is processed from the correct node
                    last_chained_node = chained_nodes[-1]
                    if last_chained_node.get('type') in ['question', 'buttons', 'list']:
                        state["rule_current_node_id"] = last_chained_node['id']
                        logger.info(f"🔗 CHAINING STATE UPDATE - Updated rule_current_node_id to {last_chained_node.get('type')} node: {last_chained_node['id']}")
                        # Store the updated state immediately
                        redis_service = RedisService()
                        redis_service.store_conversation_state(conversation_id, state)
                
                # Check if the last node in the chain is the final node
                # Use the chain_ends_at_final_node information from _collect_chained_nodes
                # If there are chained nodes, use the chain_ends_at_final_node flag
                # If no chained nodes, check if current node has no outgoing edges
                if len(chained_nodes) > 0:
                    final_node_is_last = chain_ends_at_final_node
                    last_chained_node_id = chained_nodes[-1]['id']
                    logger.info(f"🔍 CHAIN END CHECK - Last chained node {last_chained_node_id}, chain_ends_at_final_node: {chain_ends_at_final_node}")
                else:
                    # No chained nodes, so check current node
                    final_node_is_last = is_last_node
                    logger.info(f"🔍 CHAIN END CHECK - No chained nodes, current node {next_node.node_id} is_last_node: {is_last_node}")

                # If the final node has no outgoing edges, mark conversation as completed
                if final_node_is_last:
                    logger.info(f"🏁 CHAIN COMPLETION - Final node has no outgoing edges, marking conversation as completed")

                    # Mark conversation as completed in state and database
                    state["completed"] = True
                    state["ended"] = True
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database to mark as completed
                    update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
                    logger.info(f"💾 CHAIN COMPLETION - Database updated, conversation {conversation_id} marked as completed=True, ended=True")
                
                # If this is the last node, publish completion event instead of response event
                if final_node_is_last:
                    completion_node_id = chained_nodes[-1]['id'] if len(chained_nodes) > 0 else next_node.node_id
                    logger.info(f"🏁 LAST NODE REACHED (SENDMESSAGE CHAIN) - Publishing completion event for final node {completion_node_id}")
                    logger.info(f"🏁 SENDMESSAGE CHAIN SUMMARY - Started from {next_node.node_id}, collected {len(chained_nodes)} chained nodes, total nodes: {len(all_nodes)}")

                    # For rule-based chatbots, message should be null (content is in nodeDetails)
                    completion_message = None

                    # Log entity details with ownerId for last node (sendMessage chain)
                    entity_details = state.get("entity_details", [])
                    logger.info(f"📊 LAST NODE ENTITY DETAILS (SENDMESSAGE CHAIN) - Count: {len(entity_details)}")

                    # Normalize and enrich entity details to ensure correct format
                    auth_token = state.get("auth_token") or "dummy_token"
                    normalized_entity_details = []

                    for i, entity in enumerate(entity_details, 1):
                        # Support both old and new formats
                        entity_id = entity.get('id') or entity.get('entityId')
                        entity_type = entity.get('entityType') or entity.get('entity')
                        owner_id = entity.get('ownerId')
                        entity_name = entity.get('entityName') or entity.get('name')

                        # Normalize entity type to uppercase
                        if entity_type:
                            entity_type = entity_type.upper()

                            # If ownerId or entityName is missing, fetch from API
                            if (not owner_id or not entity_name) and entity_id and entity_type:
                                logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                try:
                                    entity_field_service = EntityFieldService()
                                    entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                    fetched_owner_id = entity_info.get("ownerId")
                                    fetched_entity_name = entity_info.get("entityName")
                                    
                                    if fetched_owner_id:
                                        owner_id = fetched_owner_id
                                        logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                    else:
                                        logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                        # Keep existing owner_id if API call failed
                                    
                                    if fetched_entity_name:
                                        entity_name = fetched_entity_name
                                        logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                    else:
                                        logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                        # Keep existing entity_name if API call failed
                                        
                                except Exception as e:
                                    logger.error(f"❌ Error fetching entity details for entity {i}: {str(e)}")
                                    logger.warning(f"⚠️ API ENRICHMENT FAILED - Entity {i}: Keeping existing values (ownerId={owner_id}, entityName={entity_name})")
                        # Always use the new format
                        normalized_entity = {
                            "entityId": int(entity_id) if entity_id else None,
                            "entityType": entity_type,
                            "ownerId": int(owner_id) if owner_id else None,
                            "entityName": entity_name
                        }
                        normalized_entity_details.append(normalized_entity)
                        logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")

                    await conversation_event_publisher.publish_conversation_completion(
                        chatbot_conversation_id=conversation_id,
                        completion_message=completion_message,
                        charge=0,
                        chatbot_type="RULE",
                        tenant_id=state.get("tenant_id"),
                        entity_details=normalized_entity_details,  # Use normalized format
                        node_details=all_nodes  # Array of all nodes
                    )

                    logger.info(f"✅ COMPLETION EVENT PUBLISHED (SENDMESSAGE CHAIN) - For last node {next_node.node_id} with message: {completion_message}")

                    # Clear Redis state after successful completion
                    redis_service.clear_conversation_state(conversation_id)
                    logger.info(f"🧹 REDIS STATE CLEARED - Conversation {conversation_id} completed")
                else:
                    # For non-last nodes, publish response event
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=None,  # Use nodeDetails array instead of message
                        completed=False,  # Not completed yet
                        charge=0,
                        message_conversation_id=state.get("messageConversationId"),
                        extra={
                            "chatbotType": state.get("chatbotType"),
                            "nodeDetails": all_nodes  # Array of all nodes
                        }
                    )
                    
                    logger.info(f"🔗 BATCH PUBLISHING - Published {len(all_nodes)} nodes in single event, completed: false")
            else:
                # For non-sendMessage nodes, publish normally
                # For question, buttons, and list nodes, use nodeDetails only (message=None)
                publish_message = None if node_type in ["question", "buttons", "list"] else next_message
                
                # If this is the last node, publish completion event instead of response event
                if is_last_node:
                    logger.info(f"🏁 LAST NODE REACHED - Publishing completion event for node {next_node.node_id}")

                    # Mark conversation as completed in state and database
                    state["completed"] = True
                    state["ended"] = True
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database to mark as completed
                    update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
                    logger.info(f"💾 DATABASE UPDATED - Conversation {conversation_id} marked as completed=True, ended=True")

                    # For rule-based chatbots, message should be null (content is in nodeDetails)
                    completion_message = None

                    # Log entity details with ownerId for last node
                    entity_details = state.get("entity_details", [])
                    logger.info(f"📊 LAST NODE ENTITY DETAILS - Count: {len(entity_details)}")

                    # Normalize and enrich entity details to ensure correct format
                    auth_token = state.get("auth_token") or "dummy_token"
                    normalized_entity_details = []

                    for i, entity in enumerate(entity_details, 1):
                        # Support both old and new formats
                        entity_id = entity.get('id') or entity.get('entityId')
                        entity_type = entity.get('entityType') or entity.get('entity')
                        owner_id = entity.get('ownerId')
                        entity_name = entity.get('entityName') or entity.get('name')

                        # Normalize entity type to uppercase
                        if entity_type:
                            entity_type = entity_type.upper()

                            # If ownerId or entityName is missing, fetch from API
                            if (not owner_id or not entity_name) and entity_id and entity_type:
                                logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                try:
                                    entity_field_service = EntityFieldService()
                                    entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                    fetched_owner_id = entity_info.get("ownerId")
                                    fetched_entity_name = entity_info.get("entityName")
                                    
                                    if fetched_owner_id:
                                        owner_id = fetched_owner_id
                                        logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                    else:
                                        logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                        # Keep existing owner_id if API call failed
                                    
                                    if fetched_entity_name:
                                        entity_name = fetched_entity_name
                                        logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                    else:
                                        logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                        # Keep existing entity_name if API call failed
                                        
                                except Exception as e:
                                    logger.error(f"❌ Error fetching entity details for entity {i}: {str(e)}")
                                    logger.warning(f"⚠️ API ENRICHMENT FAILED - Entity {i}: Keeping existing values (ownerId={owner_id}, entityName={entity_name})")
                        # Always use the new format
                        normalized_entity = {
                            "entityId": int(entity_id) if entity_id else None,
                            "entityType": entity_type,
                            "ownerId": int(owner_id) if owner_id else None,
                            "entityName": entity_name
                        }
                        normalized_entity_details.append(normalized_entity)
                        logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")

                    await conversation_event_publisher.publish_conversation_completion(
                        chatbot_conversation_id=conversation_id,
                        completion_message=completion_message,
                        charge=0,
                        chatbot_type="RULE",
                        tenant_id=state.get("tenant_id"),
                        entity_details=normalized_entity_details,  # Use normalized format
                        node_details=node_details
                    )

                    logger.info(f"✅ COMPLETION EVENT PUBLISHED - For last node {next_node.node_id} with message: {completion_message}")

                    # Clear Redis state after successful completion
                    redis_service.clear_conversation_state(conversation_id)
                    logger.info(f"🧹 REDIS STATE CLEARED - Conversation {conversation_id} completed")
                else:
                    # For non-last nodes, publish response event
                    # Debug: Log what we're sending
                    if node_details is None:
                        logger.error(f"❌ NODE DETAILS IS NONE - Cannot publish button node {next_node.node_id}")
                        return
                    
                    # Check if node_details has the required fields
                    if 'data' not in node_details:
                        logger.error(f"❌ NODE DETAILS MISSING DATA FIELD - Node {next_node.node_id}, node_details: {node_details}")
                        return
                    
                    extra_data = {
                        "chatbotType": state.get("chatbotType"),
                        "nodeDetails": [node_details]  # Send as array
                    }
                    logger.info(f"📤 PUBLISHING BUTTON NODE - Extra data: {extra_data}")
                    
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=publish_message,
                        completed=False,  # Not completed yet
                        charge=0,
                        message_conversation_id=state.get("messageConversationId"),
                        extra=extra_data
                    )
                    
                    logger.info(f"📤 NODE PUBLISHED - Node {next_node.node_id}, completed: false")

        except Exception as e:
            logger.error(f"Error in rule-based conversation logic: {str(e)}")
            raise

    # ==== Helper methods for RULE-based traversal ====
    def _get_start_node(self, db: Session, state: Dict[str, Any]):
        from app.models import ChatbotNode
        # Much more efficient: directly query for nodes marked as first
        start_node = db.query(ChatbotNode).filter(
            ChatbotNode.chatbot_id == state.get("chatbot_id"),
            ChatbotNode.tenant_id == state.get("tenant_id"),
            ChatbotNode.is_first_node == True
        ).first()
        
        if start_node:
            logger.info(f"🏁 START NODE FOUND - Using stored first node: {start_node.node_id}")
            return start_node
        
        # Fallback: if no first node found, get any node (shouldn't happen with proper data)
        fallback_node = db.query(ChatbotNode).filter(
            ChatbotNode.chatbot_id == state.get("chatbot_id"),
            ChatbotNode.tenant_id == state.get("tenant_id")
        ).first()
        
        if fallback_node:
            logger.warning(f"🏁 START NODE FALLBACK - No first node found, using: {fallback_node.node_id}")
        
        return fallback_node

    def _get_outgoing_edges(self, db: Session, state: Dict[str, Any], source_node_id: str):
        from app.models import ChatbotEdge
        return db.query(ChatbotEdge).filter(
            ChatbotEdge.chatbot_id == state.get("chatbot_id"),
            ChatbotEdge.tenant_id == state.get("tenant_id"),
            ChatbotEdge.source_node == source_node_id
        ).all()

    def _load_node(self, db: Session, state: Dict[str, Any], node_id: str):
        from app.models import ChatbotNode
        return db.query(ChatbotNode).filter(
            ChatbotNode.chatbot_id == state.get("chatbot_id"),
            ChatbotNode.tenant_id == state.get("tenant_id"),
            ChatbotNode.node_id == node_id
        ).first()

    # NOTE: _is_first_node method removed - now using stored is_first_node field from database
    # This provides much better performance by avoiding repeated edge queries

    def _normalize_text(self, val: Optional[str]) -> str:
        return (val or "").strip().lower()
    
    def _is_valid_input_for_node(self, node, user_input: Optional[str]) -> bool:
        """
        Check if user input is valid for a given node.
        This prevents processing stale inputs after the conversation has moved forward.
        
        Args:
            node: The node to validate against
            user_input: User's input to validate
            
        Returns:
            bool: True if input is valid for this node, False otherwise
        """
        try:
            node_type = (node.type or "").strip()
            node_data = node.data or {}
            
            # Handle empty/None input (including string 'None' sent when media is uploaded)
            is_empty_or_none = (not user_input or not user_input.strip() or 
                               (user_input and user_input.strip().lower() == 'none'))
            
            logger.info(f"🔍 MEDIA CHECK - user_input: '{user_input}', is_empty_or_none: {is_empty_or_none}, node_type: '{node_type}'")
            
            if is_empty_or_none:
                # For question nodes, check if isMedia=true (allows media-only input)
                if node_type == "question":
                    options = node_data.get("options") or []
                    is_media = node_data.get("isMedia", False)
                    
                    logger.info(f"🔍 MEDIA VALIDATION - Question node: options={options}, isMedia={is_media}")
                    
                    # If isMedia=true and no options (free text), allow empty input (media upload)
                    if is_media and not options:
                        logger.info(f"✅ MEDIA ALLOWED - Accepting media-only input for question with isMedia=true")
                        return True
                    else:
                        logger.info(f"❌ MEDIA REJECTED - isMedia={is_media}, has_options={bool(options)}")
                
                # Otherwise, empty input is invalid
                logger.info(f"❌ EMPTY INPUT INVALID - Returning False")
                return False
            
            if node_type == "buttons":
                # Check if input matches any button ID, name, or text
                buttons = node_data.get("buttons") or []
                for btn in buttons:
                    if (btn.get("id") == user_input or
                        btn.get("name") == user_input or
                        self._normalize_text(btn.get("text")) == self._normalize_text(user_input)):
                        return True
                return False
                
            elif node_type == "question":
                # Check if input matches any option text/name or numeric selection
                options = node_data.get("options") or []
                
                # Questions without options accept any free text input
                if not options:
                    return True
                
                normalized_input = self._normalize_text(user_input)
                
                # Check text/name match
                for opt in options:
                    if (self._normalize_text(opt.get("text")) == normalized_input or
                        self._normalize_text(opt.get("name")) == normalized_input):
                        return True
                
                # Check numeric selection (e.g., "1", "2", "3")
                import re as _re
                m = _re.match(r"\s*(\d+)\.?\s*$", normalized_input)
                if m:
                    idx = int(m.group(1)) - 1
                    if 0 <= idx < len(options):
                        return True
                
                return False
                
            elif node_type == "list":
                # Check if input matches any row ID or text
                sections = node_data.get("sections") or []
                
                # Lists without sections accept any free text input
                if not sections:
                    return True
                
                normalized_input = self._normalize_text(user_input)
                
                for sec in sections:
                    rows = sec.get("rows") or []
                    for row in rows:
                        if (self._normalize_text(row.get("id")) == normalized_input or
                            self._normalize_text(row.get("text")) == normalized_input):
                            return True
                
                return False
            
            else:
                # For other node types (sendMessage, condition, etc.), any input is valid
                return True
                
        except Exception as e:
            logger.error(f"Error validating input for node: {str(e)}")
            # On error, assume input is valid to avoid blocking legitimate messages
            return True
    
    def _is_valid_option_input(self, current_node, user_input: Optional[str]) -> bool:
        """
        Check if user input is a valid option for a question or list node.
        
        Args:
            current_node: The current question or list node
            user_input: User's input to validate
            
        Returns:
            bool: True if input matches a valid option, False otherwise
        """
        try:
            node_data = current_node.data or {}
        except Exception:
            node_data = {}
            
        node_type = (current_node.type or "").strip()
        if node_type not in ["question", "list"]:
            return True  # Non-question/list nodes don't have option validation
        
        # Handle empty/None input (including string 'None' sent when media is uploaded)
        is_empty_or_none = (not user_input or not user_input.strip() or 
                           (user_input and user_input.strip().lower() == 'none'))
        
        if is_empty_or_none:
            # For question nodes, check if isMedia=true (allows media-only input)
            if node_type == "question":
                options = node_data.get("options") or []
                is_media = node_data.get("isMedia", False)
                
                # If isMedia=true and no options (free text), allow empty input (media upload)
                if is_media and not options:
                    return True
            
            # Otherwise, empty input is invalid
            return False
            
        if node_type == "question":
            options = node_data.get("options") or []
            if not options:
                return True  # Questions without options accept any input
                
            normalized_input = self._normalize_text(user_input)
            
            # Check direct text/name match
            for opt in options:
                if (self._normalize_text(opt.get("text")) == normalized_input or 
                    self._normalize_text(opt.get("name")) == normalized_input):
                    return True
                    
            # Check numeric selection (e.g., "1", "1.")
            import re as _re
            m = _re.match(r"\s*(\d+)\.?\s*$", normalized_input)
            if m:
                try:
                    idx = int(m.group(1)) - 1
                    if 0 <= idx < len(options):
                        return True
                except Exception:
                    pass
                    
        elif node_type == "list":
            sections = node_data.get("sections") or []
            if not sections:
                return True  # Lists without sections accept any input
                
            normalized_input = self._normalize_text(user_input)
            
            # Check if input matches any row ID or text in any section
            for section in sections:
                rows = section.get("rows", [])
                for row in rows:
                    row_id = row.get("id", "")
                    row_text = row.get("text", "")
                    
                    if (self._normalize_text(row_id) == normalized_input or
                        self._normalize_text(row_text) == normalized_input):
                        return True
                        
            # Check numeric selection (e.g., "1", "1.")
            import re as _re
            m = _re.match(r"\s*(\d+)\.?\s*$", normalized_input)
            if m:
                try:
                    # Count total rows across all sections
                    total_rows = sum(len(section.get("rows", [])) for section in sections)
                    idx = int(m.group(1)) - 1
                    if 0 <= idx < total_rows:
                        return True
                except Exception:
                    pass
                    
        return False

    async def _re_ask_question_with_error(
        self,
        db: Session,
        state: Dict[str, Any],
        current_node,
        conversation_id: str,
        invalid_input: str
    ):
        """
        Re-ask the current question with an error message for invalid option selection.
        
        Args:
            db: Database session
            state: Conversation state
            current_node: Current question node
            conversation_id: Conversation ID
            invalid_input: The invalid input provided by user
        """
        try:
            # Get entity field service for variable substitution
            entity_field_service = EntityFieldService()
            entity_details = state.get("entity_details", [])
            
            # Get field values for variable substitution
            field_values = {}
            if entity_details:
                # Get auth token from state
                auth_token = state.get("auth_token") or "dummy_token"
                
                # Get variable mappings from current node
                variable_mappings = []
                if hasattr(current_node, 'variable_mapping') and current_node.variable_mapping:
                    variable_mappings = current_node.variable_mapping
                elif hasattr(current_node, 'data') and current_node.data:
                    variable_mappings = current_node.data.get("variableMapping") or []
                
                if variable_mappings:
                    allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id"))
                    field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, variable_mappings, auth_token, allowed_entities)
            
            # Get node data and apply variable substitution
            try:
                node_data = current_node.data or {}
            except Exception:
                node_data = {}
            
            substituted_data = entity_field_service.substitute_node_variables(node_data, field_values)
            question_text = substituted_data.get("text") or ""
            options = substituted_data.get("options") or []
            
            # Create error message
            error_message = "Please select a correct input"
            
            # Build the re-ask message with error and question
            if options:
                option_lines = []
                for idx, opt in enumerate(options, 1):
                    option_text = opt.get("text", "")
                    if option_text:
                        option_lines.append(f"{idx}. {option_text}")
                
                if option_lines:
                    full_message = f"{error_message}\n\n{question_text}\n" + "\n".join(option_lines)
                else:
                    full_message = f"{error_message}\n\n{question_text}"
            else:
                full_message = f"{error_message}\n\n{question_text}"
            
            # Create node details for the re-asked question
            node_details = {
                "id": current_node.node_id,
                "name": getattr(current_node, "name", None),
                "type": "question",
                "isFirstNode": False,
                "data": {
                    "text": full_message,
                    "options": [{"text": opt.get("text"), "name": opt.get("name"), "position": opt.get("position", idx)} 
                               for idx, opt in enumerate(options)]
                }
            }
            
            logger.info(f"🔄 RE-ASKING QUESTION - Invalid input '{invalid_input}' provided, re-asking question with error message")
            
            # Publish the re-ask question response
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=None,  # For rule-based chatbots, message should be null
                completed=False,
                charge=0,
                tenant_id=state.get("tenant_id"),
                message_conversation_id=state.get("messageConversationId"),
                extra={
                    "chatbotType": "RULE",
                    "nodeDetails": [node_details]  # Send as a list with single node
                }
            )
            
        except Exception as e:
            logger.error(f"Error re-asking question with error message: {str(e)}")
            # Fallback: just re-ask the original question without error message
            try:
                node_data = current_node.data or {}
                question_text = node_data.get("text") or ""
                await conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=question_text,
                    completed=False,
                    charge=0,
                    tenant_id=state.get("tenant_id"),
                    message_conversation_id=state.get("messageConversationId")
                )
            except Exception as fallback_error:
                logger.error(f"Fallback re-ask also failed: {str(fallback_error)}")

    async def _re_ask_question_with_media_error(
        self,
        db: Session,
        state: Dict[str, Any],
        current_node,
        conversation_id: str
    ):
        """
        Re-ask the current question with an error message when media is uploaded but not allowed.
        
        Args:
            db: Database session
            state: Conversation state
            current_node: Current question node
            conversation_id: Conversation ID
        """
        try:
            # Get entity field service for variable substitution
            entity_field_service = EntityFieldService()
            entity_details = state.get("entity_details", [])
            
            # Get field values for variable substitution
            field_values = {}
            if entity_details:
                # Get auth token from state
                auth_token = state.get("auth_token") or "dummy_token"
                
                # Get variable mappings from current node
                variable_mappings = []
                if hasattr(current_node, 'variable_mapping') and current_node.variable_mapping:
                    variable_mappings = current_node.variable_mapping
                elif hasattr(current_node, 'data') and current_node.data:
                    variable_mappings = current_node.data.get("variableMapping") or []
                
                if variable_mappings:
                    allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id"))
                    field_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, variable_mappings, auth_token, allowed_entities)
            
            # Get node data and apply variable substitution
            try:
                node_data = current_node.data or {}
            except Exception:
                node_data = {}
            
            substituted_data = entity_field_service.substitute_node_variables(node_data, field_values)
            question_text = substituted_data.get("text") or ""
            options = substituted_data.get("options") or []
            
            # Create error message for media upload
            error_message = "Please provide a text response. Media uploads are not accepted for this question."
            
            # Build the re-ask message with error and question
            full_message = f"{error_message}\n\n{question_text}"
            
            # Create node details for the re-asked question
            node_details = {
                "id": current_node.node_id,
                "name": getattr(current_node, "name", None),
                "type": "question",
                "isFirstNode": False,
                "data": {
                    "text": full_message,
                    "options": options
                }
            }
            
            logger.info(f"🔄 RE-ASKING QUESTION - Media uploaded but not allowed, re-asking question with error message")
            
            # Publish the re-ask question response
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=None,  # For rule-based chatbots, message should be null
                completed=False,
                charge=0,
                tenant_id=state.get("tenant_id"),
                message_conversation_id=state.get("messageConversationId"),
                extra={
                    "chatbotType": "RULE",
                    "nodeDetails": [node_details]  # Send as a list with single node
                }
            )
            
        except Exception as e:
            logger.error(f"Error re-asking question with media error message: {str(e)}")
            # Fallback: just re-ask the original question without error message
            try:
                node_data = current_node.data or {}
                question_text = node_data.get("text") or ""
                await conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=question_text,
                    completed=False,
                    charge=0,
                    tenant_id=state.get("tenant_id"),
                    message_conversation_id=state.get("messageConversationId")
                )
            except Exception as fallback_error:
                logger.error(f"Fallback re-ask also failed: {str(fallback_error)}")

    def _select_edge_for_input(self, current_node, outgoing, user_input: Optional[str]):
        if not outgoing:
            return None
        
        # Helper function to find default edge
        def find_default_edge(edges):
            """Find edge with sourceHandle='default'"""
            for edge in edges:
                if edge.source_handle == "default":
                    return edge
            return None
        
        # If no input provided, handle based on node type (including string 'None' from media uploads)
        is_empty_or_none = (not user_input or not user_input.strip() or 
                           (user_input and user_input.strip().lower() == 'none'))
        
        if is_empty_or_none:
            node_type = (current_node.type or "").strip()
            if node_type in ["question", "list"]:
                # For question nodes, check if isMedia=true (allows media-only input)
                if node_type == "question":
                    try:
                        node_data = current_node.data or {}
                        is_media = node_data.get("isMedia", False)
                        options = node_data.get("options") or []
                        
                        # If isMedia=true and no options (free text), allow proceeding to default edge or first edge
                        if is_media and not options:
                            logger.info(f"📸 MEDIA QUESTION - Proceeding to next node with media-only input")
                            default_edge = find_default_edge(outgoing)
                            if default_edge:
                                logger.info(f"📸 MEDIA QUESTION - Using default edge to {default_edge.target_node}")
                                return default_edge
                            return outgoing[0] if outgoing else None
                    except Exception as e:
                        logger.debug(f"Error checking isMedia: {e}")
                
                # For question and list nodes with options, check for default edge first
                default_edge = find_default_edge(outgoing)
                if default_edge:
                    logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for empty input on {node_type} node")
                    return default_edge
                return None
            else:
                # For other nodes, check for default edge first, then fallback to first edge
                default_edge = find_default_edge(outgoing)
                if default_edge:
                    logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for empty input on {node_type} node")
                    return default_edge
                return outgoing[0]
        try:
            node_data = current_node.data or {}
        except Exception:
            node_data = {}
        normalized_input = self._normalize_text(user_input)

        # Match by type
        node_type = (current_node.type or "").strip()
        # question: options with name/text and numeric selection support (1, 2, 3)
        if node_type == "question":
            options = (node_data.get("options") or [])
            option_matched = False

            logger.info(f"🔍 QUESTION EDGE SELECTION - Processing question node with {len(options)} options, {len(outgoing)} edges")

            # First try direct text/name match
            for idx, opt in enumerate(options):
                if self._normalize_text(opt.get("text")) == normalized_input or self._normalize_text(opt.get("name")) == normalized_input:
                    option_matched = True
                    handle = opt.get("name") or opt.get("text")
                    edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(handle or "")), None)
                    if not edge:
                        # Also try the option-{index} format commonly used in React Flow
                        option_handle = f"option-{idx}"
                        edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == option_handle), None)
                    if edge:
                        logger.info(f"✅ QUESTION EDGE FOUND - Option {idx} matched, returning edge to {edge.target}")
                        return edge
                    # If option was matched but no edge found, return None to trigger terminal handling
                    logger.info(f"⚠️ QUESTION OPTION SELECTED - No edge configured for option {idx} (text: {opt.get('text')}) - RETURNING NONE")
                    return None

            # Next try numeric selection (e.g., "1", "1.")
            if not option_matched:
                import re as _re
                m = _re.match(r"\s*(\d+)\.?\s*$", normalized_input)
                logger.info(f"🔍 QUESTION NUMERIC CHECK - Input: '{user_input}', Normalized: '{normalized_input}', Regex match: {m is not None}")
                if m:
                    try:
                        idx = int(m.group(1)) - 1
                        logger.info(f"🔍 QUESTION NUMERIC INDEX - User input '{user_input}' → Index {idx}")
                        if 0 <= idx < len(options):
                            option_matched = True
                            opt = options[idx]
                            handle = opt.get("name") or opt.get("text") or str(idx + 1)
                            logger.info(f"🔍 QUESTION NUMERIC OPTION - Matched option {idx}: {opt}, handle: '{handle}'")
                            logger.info(f"🔍 QUESTION EDGES DEBUG - Total outgoing edges: {len(outgoing)}")
                            for i, e in enumerate(outgoing):
                                logger.info(f"    Edge {i}: source_handle='{e.source_handle}', target='{e.target_node}'")
                            
                            # Try match by handle or by numeric index
                            edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(handle)), None)
                            logger.info(f"🔍 QUESTION EDGE MATCH 1 - Tried handle '{handle}': {edge is not None}")
                            if not edge:
                                edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == str(idx + 1)), None)
                                logger.info(f"🔍 QUESTION EDGE MATCH 2 - Tried numeric '{idx + 1}': {edge is not None}")
                            # Also try the option-{index} format commonly used in React Flow
                            if not edge:
                                option_handle = f"option-{idx}"
                                edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == option_handle), None)
                                logger.info(f"🔍 QUESTION EDGE MATCH 3 - Tried 'option-{idx}': {edge is not None}")
                            if edge:
                                logger.info(f"✅ QUESTION EDGE FOUND - Numeric option {idx} matched, returning edge to {edge.target_node}")
                                return edge
                            # If option was matched but no edge found, return None to trigger terminal handling
                            logger.info(f"⚠️ QUESTION OPTION SELECTED - No edge configured for numeric option {idx} (text: {opt.get('text')}) - RETURNING NONE")
                            return None
                        else:
                            logger.info(f"⚠️ QUESTION NUMERIC OUT OF RANGE - Index {idx} not in range 0-{len(options)-1}")
                    except Exception as e:
                        logger.error(f"❌ QUESTION NUMERIC ERROR - {str(e)}")
                        pass

            # If no options or no match found, handle accordingly
            if not options:
                # Free-text question - check if there are any outgoing edges
                if outgoing and len(outgoing) > 0:
                    # Check for default edge first
                    default_edge = find_default_edge(outgoing)
                    if default_edge:
                        logger.info(f"✅ QUESTION FREE-TEXT - Using default edge to {default_edge.target_node}")
                        return default_edge
                    # Has edges → proceed through the first edge after user provides answer
                    logger.info(f"✅ QUESTION FREE-TEXT - Question has no options but has {len(outgoing)} edges, proceeding through first edge to {outgoing[0].target_node}")
                    return outgoing[0]
                else:
                    # No edges → trigger terminal handling (completion)
                    logger.info(f"🏁 QUESTION FREE-TEXT TERMINAL - Question has no options and no edges, returning None to trigger completion")
                    return None
            else:
                logger.info(f"🔍 QUESTION NO MATCH - No option matched for input '{user_input}', checking for default edge")
                # Check for default edge when no option matches
                default_edge = find_default_edge(outgoing)
                if default_edge:
                    logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for unmatched input on question node")
                    return default_edge
            return None
        # buttons: buttons with id, name, or text
        if node_type == "buttons":
            for btn in (node_data.get("buttons") or []):
                # Check if this button matches the user input
                button_matched = False

                # First try to match by button ID (primary field - what frontend sends)
                if btn.get("id") == user_input:
                    button_matched = True
                    handle = btn.get("id")
                    edge = next((e for e in outgoing if (e.source_handle or "") == (handle or "")), None)
                    if edge:
                        return edge

                # Try to match by button name (fallback)
                if not button_matched and btn.get("name") == user_input:
                    button_matched = True
                    handle = btn.get("name")
                    edge = next((e for e in outgoing if (e.source_handle or "") == (handle or "")), None)
                    if edge:
                        return edge

                # Fallback to text matching for backward compatibility
                if not button_matched and self._normalize_text(btn.get("text")) == normalized_input:
                    button_matched = True
                    handle = btn.get("id") or btn.get("name") or btn.get("text")
                    edge = next((e for e in outgoing if (e.source_handle or "") == (handle or "")), None)
                    if edge:
                        return edge

                # If button was matched but no edge found, return None to trigger terminal handling
                if button_matched:
                    logger.info(f"⚠️ BUTTON SELECTED - No edge configured for button {btn.get('id')} (text: {btn.get('text')})")
                    return None
            
            # If no button matched, check for default edge
            default_edge = find_default_edge(outgoing)
            if default_edge:
                logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for unmatched button input")
                return default_edge
        # list: attempt match against row ID, text, or numeric selection
        if node_type == "list":
            sections = node_data.get("sections") or []
            
            # First try direct match by row ID or text
            for sec in sections:
                rows = sec.get("rows") or []
                for idx, row in enumerate(rows):
                    row_id = row.get("id", "")
                    row_text = row.get("text", "")
                    
                    # Check if input matches this specific row
                    if (self._normalize_text(row_id) == normalized_input or
                        self._normalize_text(row_text) == normalized_input):
                        
                        # We found a matching row, now try to find the best edge for this row
                        # Try to find edge that matches the specific input first
                        
                        # If input matches row ID, try to find edge by row ID first
                        if row_id and self._normalize_text(row_id) == normalized_input:
                            edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(row_id)), None)
                            if edge:
                                return edge
                        
                        # If input matches row text, try to find edge by row text first
                        if row_text and self._normalize_text(row_text) == normalized_input:
                            edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(row_text)), None)
                            if edge:
                                return edge
                        
                        # If no specific match found, try other possibilities for this row
                        # Try to find edge by row ID
                        if row_id:
                            edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(row_id)), None)
                            if edge:
                                return edge
                        
                        # Try to find edge by row text
                        if row_text:
                            edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(row_text)), None)
                            if edge:
                                return edge
                        
                        # Try list-{index} format as fallback
                        list_handle = f"list-{idx}"
                        edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == list_handle), None)
                        if edge:
                            return edge
                        
                        # If still no edge found for this specific row, return None
                        # This will trigger the "terminal node" handler to properly end the conversation
                        logger.info(f"⚠️ LIST ROW SELECTED - No edge configured for row {row_id} (text: {row_text})")
                        return None
            
            # Try numeric selection (e.g., "1", "2")
            import re as _re
            m = _re.match(r"\s*(\d+)\.?\s*$", normalized_input)
            if m:
                try:
                    idx = int(m.group(1)) - 1
                    # Count total rows across all sections
                    total_rows = sum(len(section.get("rows", [])) for section in sections)
                    if 0 <= idx < total_rows:
                        # Find the row at this index
                        current_idx = 0
                        for section in sections:
                            rows = section.get("rows", [])
                            for row_idx, row in enumerate(rows):
                                if current_idx == idx:
                                    row_id = row.get("id", "")
                                    row_title = row.get("title", "")
                                    
                                    # For numeric selection, prioritize list-{global_index} format first
                                    list_handle = f"list-{current_idx}"
                                    edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == list_handle), None)
                                    if edge:
                                        return edge
                                    
                                    # Try to find edge by row ID
                                    if row_id:
                                        edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(row_id)), None)
                                        if edge:
                                            return edge
                                    
                                    # Try to find edge by row title
                                    if row_title:
                                        edge = next((e for e in outgoing if self._normalize_text(e.source_handle or "") == self._normalize_text(row_title)), None)
                                        if edge:
                                            return edge
                                    
                                    # If no edge found for this specific row, return None
                                    logger.info(f"⚠️ LIST ROW SELECTED (numeric) - No edge configured for row {row_id}")
                                    return None
                                current_idx += 1
                            if current_idx > idx:
                                break
                except Exception:
                    pass
            
            # If no match found, check for default edge
            default_edge = find_default_edge(outgoing)
            if default_edge:
                logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for unmatched list input")
                return default_edge
            return None
        # condition: simple equality against value field on first condition
        if node_type == "condition":
            for cond in (node_data.get("conditions") or []):
                if self._normalize_text(cond.get("value")) == normalized_input:
                    handle = cond.get("name") or cond.get("value")
                    edge = next((e for e in outgoing if (e.source_handle or "") == (handle or "")), None)
                    if edge:
                        return edge
            
            # If no condition matched, check for default edge
            default_edge = find_default_edge(outgoing)
            if default_edge:
                logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for unmatched condition input")
                return default_edge

        # Fallback behavior: for question/list nodes with options, return None to trigger re-ask
        # For other nodes, fallback to first edge
        if node_type == "question":
            options = (node_data.get("options") or [])
            if options:
                # Question node with options but no valid match found - check for default edge
                default_edge = find_default_edge(outgoing)
                if default_edge:
                    logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for unmatched question input")
                    return default_edge
                return None
        elif node_type == "list":
            sections = (node_data.get("sections") or [])
            if sections:
                # List node with sections but no valid match found - check for default edge
                default_edge = find_default_edge(outgoing)
                if default_edge:
                    logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge for unmatched list input")
                    return default_edge
                return None
        
        # Fallback to default edge first, then first edge for non-question/list nodes or questions/lists without options
        default_edge = find_default_edge(outgoing)
        if default_edge:
            logger.info(f"🔍 DEFAULT EDGE FOUND - Using default edge as fallback")
            return default_edge
        return outgoing[0] if outgoing else None

    def _build_message_for_node(self, node, field_values=None):
        try:
            node_data = node.data or {}
        except Exception:
            node_data = {}
        
        # Apply variable substitution if field_values provided
        if field_values:
            from app.services.entity_field_service import EntityFieldService
            entity_field_service = EntityFieldService()
            node_data = entity_field_service.substitute_node_variables(node_data, field_values)
        
        node_type = (getattr(node, "type", "") or "").strip()
        # question: text + options
        if node_type == "question":
            prompt = node_data.get("text") or ""
            options = node_data.get("options") or []
            if options:
                opt_lines = [f"- {opt.get('text', '')}" for opt in options]
                prompt = (prompt + "\n" + "\n".join(opt_lines)).strip()
            return prompt or ""
        # sendMessage: either text or options blocks
        if node_type == "sendMessage":
            text = node_data.get("text") or ""
            blocks = []
            media_blocks = []
            for opt in (node_data.get("options") or []):
                opt_type = opt.get("type")
                if opt_type == "text" and opt.get("text"):
                    blocks.append(opt.get("text"))
                elif opt_type == "media":
                    # Capture media directive; downstream renderer/consumer can use mediaFile
                    media_entry = {k: v for k, v in opt.items() if k in ("type", "mediaFile", "text")}
                    media_blocks.append(media_entry)
            payload = {}
            if text:
                payload["text"] = text
            if blocks:
                payload["blocks"] = blocks
            if media_blocks:
                payload["media"] = media_blocks
            # Serialize to a simple string if only text, else JSON string
            if payload.keys() == {"text"}:
                return payload["text"]
            import json as _json
            return _json.dumps(payload)
        if node_type == "buttons":
            text = node_data.get("body") or node_data.get("text") or ""
            buttons = node_data.get("buttons") or []
            if buttons:
                btn_lines = [f"[{b.get('text','')}]" for b in buttons]
                if text:
                    btn_lines.insert(0, text)
                return "\n".join(btn_lines)
            return text
        if node_type == "list":
            text = node_data.get("body") or node_data.get("text") or ""
            sections = node_data.get("sections") or []
            lines = [text] if text else []
            for sec in sections:
                title = sec.get("title")
                if title:
                    lines.append(f"{title}:")
                for row in (sec.get("rows") or []):
                    row_text = row.get("text") or ""
                    if row_text:
                        lines.append(f"- {row_text}")
            return "\n".join(lines)
        if node_type == "condition":
            # conditions do not have user-facing text; move to next automatically
            return ""
        # default
        return (node_data.get("text") or "").strip()
    
    async def _continue_conversation_logic(
        self,
        db: Session,
        conversation: ChatbotConversation,
        state: Dict[str, Any],
        user_message: Optional[str],
        conversation_id: str
    ):
        """
        Continue conversation logic (extracted from existing continue_conversation endpoint)

        Args:
            db: Database session
            conversation: ChatbotConversation object
            state: Conversation state from Redis
            user_message: User's message
            conversation_id: Conversation UUID
        """
        try:
            # Clean and normalize conversation state to ensure required fields exist
            state = clean_conversation_state(state)

            # Handle None/empty messages for image upload detection
            if not user_message or not user_message.strip():
                logger.info(f"📸 IMAGE UPLOAD DETECTED in conversation logic - Message: '{user_message}'")
                # Add a placeholder message to history to maintain conversation flow
                state["history"].append({"role": "user", "content": "[Image Upload]"})
            else:
                # Update conversation history with valid message
                state["history"].append({"role": "user", "content": user_message})

            # Initialize ElasticsearchService
            logger.info(f"🧠 AI FLOW - Initializing ElasticsearchService for conversation {conversation_id}")
            es_service = ElasticsearchService()

            # Get tenant_id for token usage tracking
            tenant_id = state.get("tenant_id", "unknown")

            # Check if conversation is already completed (in knowledge search phase)
            if state.get("completed", False):
                await self._handle_knowledge_phase(
                    db, state, user_message, conversation_id, tenant_id, es_service
                )
                return

            # Process regular question-answer flow
            await self._handle_question_phase(
                db, state, user_message, conversation_id, tenant_id, es_service
            )

        except Exception as e:
            logger.error(f"Error in continue conversation logic: {str(e)}")
            raise

    async def _handle_knowledge_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        user_message: str,
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle conversation in knowledge search phase (after all questions answered)
        """
        try:
            logger.info("Processing message in knowledge phase")
            logger.info(f"🎯 KNOWLEDGE PHASE DEBUG - User message: '{user_message}', Conversation ID: {conversation_id}")

            # Check if conversation is already ended
            if state.get("ended", False):
                # Conversation is already ended, don't allow more messages
                logger.info(f"Conversation {conversation_id} is already ended, blocking message")
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message="This conversation has already ended. Thank you for using our service!",
                    charge=0,
                    chatbot_type=state.get("chatbotType", "AI"),
                    tenant_id=state.get("tenant_id"),
                    entity_details=[],
                    message_conversation_id=state.get("message_conversation_id"),
                    chatbot_id=state.get("chatbot_id"),
                    chatbot_name=state.get("chatbot_name")
                )
                return

            # Check if user has already reached the 5-question limit
            chatbot_service = ChatbotService()
            current_question_count = chatbot_service.get_conversation_questions_count(state)

            if current_question_count >= 5:
                # User has already asked 5 questions, don't allow more
                logger.info(f"User has already asked 5 questions in conversation {conversation_id}, blocking message")
                limit_response = "You've already asked 5 questions, which is our limit for this session. Your information has been completed. Thank you!"

                # Update state
                state["history"].append({"role": "assistant", "content": limit_response})
                state["ended"] = True
                redis_service = RedisService()
                redis_service.store_conversation_state(conversation_id, state)

                # Update conversation in database
                update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)

                # Normalize entity details
                normalized_entity_details = []
                entity_details = state.get("entity_details", [])
                for i, entity in enumerate(entity_details, 1):
                    entity_id = entity.get("id") or entity.get("entityId")
                    entity_type = (entity.get("entityType") or entity.get("entity") or "").upper()
                    owner_id = entity.get("ownerId")
                    entity_name = entity.get("name") or entity.get("entityName")
                    normalized_entity_details.append({
                        "entityId": int(entity_id) if entity_id else None,
                        "entityType": entity_type,
                        "ownerId": int(owner_id) if owner_id else None,
                        "entityName": entity_name
                    })
                
                # Publish limit reached response with proper format
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message=limit_response,
                    charge=0,
                    chatbot_type=state.get("chatbotType", "AI"),
                    tenant_id=state.get("tenant_id"),
                    entity_details=normalized_entity_details,
                    message_conversation_id=state.get("message_conversation_id"),
                    chatbot_id=state.get("chatbot_id"),
                    chatbot_name=state.get("chatbot_name")
                )
                
                # Publish workflow completion event if this was a workflow-triggered conversation
                await self._publish_workflow_completion_if_needed(conversation_id, state, db)
                return

            # Check if user wants to end the conversation
            conversation_context = ""
            if state.get("history"):
                recent_messages = state["history"][-4:]
                conversation_context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])

            wants_to_end, termination_input_tokens, termination_output_tokens, termination_model = es_service.detect_conversation_termination(
                user_message,
                conversation_context
            )

            if wants_to_end:
                # Generate farewell message
                conversation_summary = ""
                if state.get("answers"):
                    conversation_summary = f"We collected information about: {', '.join([ans.get('question', '') for ans in state['answers']])}"

                farewell, input_tokens, output_tokens, model = es_service.generate_farewell_message(
                    user_message,
                    conversation_summary
                )

                # Store conversation turn
                store_conversation_turn(
                    db, conversation_id, tenant_id,
                    llm_prompt=[{"role": "user", "content": user_message}],
                    llm_response=farewell,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens
                )

                # Update state
                state["history"].append({"role": "assistant", "content": farewell})
                state["ended"] = True
                redis_service = RedisService()
                redis_service.store_conversation_state(conversation_id, state)

                # Update conversation in database
                update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)

                # 🔄 Trigger entity updates (user-driven completion)
                logger.info("🔄 Starting entity update process after user-driven conversation completion")
                entity_details = state.get("entity_details", [])
                collected_answers = state.get("answers", [])

                logger.info(f"📊 Entities to update: {len(entity_details)}")
                for i, entity in enumerate(entity_details, 1):
                    logger.info(f"   Entity {i}: {entity.get('entityType', 'unknown')} (ID: {entity.get('id', 'unknown')})")

                logger.info(f"📝 Total answers collected: {len(collected_answers)}")
                for i, answer in enumerate(collected_answers, 1):
                    logger.info(f"   Answer {i}: '{answer.get('question', 'unknown')}' = '{answer.get('answer', 'unknown')}'")

                if entity_details and collected_answers:
                    try:
                        logger.info("🚀 Calling update_entities_after_conversation...")
                        chatbot_service = ChatbotService()
                        update_results = await chatbot_service.update_entities_after_conversation(
                            entity_details,
                            collected_answers,
                            tenant_id,
                            state.get("user_id", "unknown"),
                            state.get("chatbot_id"),
                            state  # Pass conversation state
                        )
                        logger.info(f"✅ Entity updates completed successfully: {update_results}")
                    except Exception as e:
                        logger.error(f"❌ Entity update failed: {str(e)}")
                else:
                    logger.warning("⚠️ No entities or answers to update - skipping entity updates")
                    if not entity_details:
                        logger.warning("   - entity_details is empty or None")
                    if not collected_answers:
                        logger.warning("   - collected_answers is empty or None")

                # Normalize entity details for completion event
                normalized_entity_details = []
                for i, entity in enumerate(entity_details, 1):
                    entity_id = entity.get("id") or entity.get("entityId")
                    entity_type = (entity.get("entityType") or entity.get("entity") or "").upper()
                    owner_id = entity.get("ownerId")
                    entity_name = entity.get("name") or entity.get("entityName")
                    normalized_entity_details.append({
                        "entityId": int(entity_id) if entity_id else None,
                        "entityType": entity_type,
                        "ownerId": int(owner_id) if owner_id else None,
                        "entityName": entity_name
                    })
                
                # Publish completion event (user-driven completion) with proper format
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message=farewell,
                    charge=0,
                    chatbot_type=state.get("chatbotType", "AI"),
                    tenant_id=state.get("tenant_id"),
                    entity_details=normalized_entity_details,
                    message_conversation_id=state.get("message_conversation_id"),
                    chatbot_id=state.get("chatbot_id"),
                    chatbot_name=state.get("chatbot_name")
                )
                
                # Publish workflow completion event if this was a workflow-triggered conversation
                await self._publish_workflow_completion_if_needed(conversation_id, state, db)
                return

            # Try to answer from knowledgebase
            chatbot_service = ChatbotService()
            has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], int(tenant_id))
            logger.info(f"🔍 Knowledge phase check - has_knowledgebase: {has_knowledgebase}")

            if has_knowledgebase:
                # Add user question to position 6 field for entity updates
                logger.info(f"📝 Adding user question to position 6 field: '{user_message}'")
                try:
                    question_result = chatbot_service.add_user_question_to_position_6(
                        state["chatbot_id"],
                        int(tenant_id),
                        user_message
                    )
                    logger.info(f"📝 Question addition result: {question_result}")
                except Exception as e:
                    logger.error(f"❌ Error adding question to position 6: {str(e)}")
                
                # Also add question to conversation state for entity updates
                logger.info(f"📝 Adding user question to conversation state: '{user_message}'")
                is_fifth_question = False
                try:
                    conversation_question_result = chatbot_service.add_question_to_conversation(
                        state,
                        user_message
                    )
                    logger.info(f"📝 Conversation question addition result: {conversation_question_result}")
                    
                    # Check if this was the 5th question (limit reached)
                    is_fifth_question = conversation_question_result.get("limit_reached", False)
                    if is_fifth_question:
                        logger.info(f"User reached 5-question limit for conversation {conversation_id}. Will answer this question and then end.")
                        
                except Exception as e:
                    logger.error(f"❌ Error adding question to conversation state: {str(e)}")

                # Search knowledgebase for answer
                logger.info(f"🧠 AI FLOW - Making OpenAI API call for knowledgebase search in conversation {conversation_id}")
                ai_response, input_tokens, output_tokens, model = es_service.search_knowledgebase_and_generate_response(
                    state["chatbot_id"],
                    tenant_id,
                    user_message,
                    state["history"],
                    db
                )

                if ai_response and ai_response.strip():
                    # Check if this is the 5th question (limit reached)
                    if is_fifth_question:
                        logger.info(f"5th question detected. Will provide answer and then end conversation.")
                        
                        # For 5th question, just provide the answer without follow-up
                        complete_response = ai_response
                        
                        # Get the thank you message from chatbot configuration
                        chatbot = db.query(Chatbot).filter(
                            Chatbot.id == state["chatbot_id"],
                            Chatbot.tenant_id == tenant_id
                        ).first()
                        
                        thank_you_message = "Thank you for using our service!"
                        if chatbot and chatbot.thank_you_message:
                            thank_you_message = chatbot.thank_you_message
                        
                        # Add thank you message to the response
                        final_response = f"{complete_response}\n\n{thank_you_message}"
                        
                        # Store conversation turn
                        prompt = [{"role": "user", "content": user_message}]
                        store_conversation_turn(
                            db, conversation_id, tenant_id,
                            llm_prompt=prompt,
                            llm_response=final_response,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens
                        )

                        # Update state to mark as ended
                        state["history"].append({"role": "assistant", "content": final_response})
                        state["completed"] = True
                        state["ended"] = True
                        redis_service = RedisService()
                        redis_service.store_conversation_state(conversation_id, state)

                        # Update conversation in database
                        update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)

                        # 🔄 Trigger entity updates for this question
                        logger.info("🔄 Triggering entity updates for 5th question (final)")
                        entity_details = state.get("entity_details", [])
                        collected_answers = state.get("answers", [])

                        if entity_details and collected_answers:
                            try:
                                update_results = await chatbot_service.update_entities_after_conversation(
                                    entity_details,
                                    collected_answers,
                                    int(tenant_id),
                                    state.get("user_id", "unknown"),
                                    state.get("chatbot_id"),
                                    state  # Pass conversation state
                                )
                                logger.info(f"✅ Entity updates completed for 5th question: {update_results}")
                            except Exception as e:
                                logger.error(f"❌ Entity update failed for 5th question: {str(e)}")
                        else:
                            logger.warning("⚠️ No entities or answers to update for 5th question")

                        # Normalize and enrich entity details for 5th question completion
                        normalized_entity_details = []
                        entity_details_5th = state.get("entity_details", [])
                        auth_token = state.get("auth_token") or "dummy_token"
                        
                        logger.info(f"🔍 AI CHATBOT COMPLETION DEBUG - Entity details count: {len(entity_details_5th)}")
                        logger.info(f"🔍 AI CHATBOT COMPLETION DEBUG - Auth token available: {bool(auth_token and auth_token != 'dummy_token')}")
                        logger.info(f"🔍 AI CHATBOT COMPLETION DEBUG - Auth token length: {len(auth_token) if auth_token else 0}")
                        
                        for i, entity in enumerate(entity_details_5th, 1):
                            # Support both old and new formats
                            entity_id = entity.get("id") or entity.get("entityId")
                            entity_type = (entity.get("entityType") or entity.get("entity") or "").upper()
                            owner_id = entity.get("ownerId")
                            entity_name = entity.get("name") or entity.get("entityName")
                            
                            logger.info(f"🔍 ENTITY {i} DEBUG - ID: {entity_id}, Type: {entity_type}, OwnerId: {owner_id}, EntityName: {entity_name}")
                            
                            # If ownerId or entityName is missing, fetch from API
                            if (not owner_id or not entity_name) and entity_id and entity_type:
                                logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                try:
                                    entity_field_service = EntityFieldService()
                                    entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                    fetched_owner_id = entity_info.get("ownerId")
                                    fetched_entity_name = entity_info.get("entityName")
                                    
                                    logger.info(f"🔍 API RESPONSE - Entity {i}: OwnerId={fetched_owner_id}, EntityName={fetched_entity_name}")
                                    
                                    if fetched_owner_id:
                                        owner_id = fetched_owner_id
                                        logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                    else:
                                        logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                        # Keep existing owner_id if API call failed
                                    
                                    if fetched_entity_name:
                                        entity_name = fetched_entity_name
                                        logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                    else:
                                        logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                        # Keep existing entity_name if API call failed
                                        
                                except Exception as e:
                                    logger.error(f"❌ Error fetching entity details for entity {i}: {str(e)}")
                                    logger.warning(f"⚠️ API ENRICHMENT FAILED - Entity {i}: Keeping existing values (ownerId={owner_id}, entityName={entity_name})")
                            else:
                                logger.info(f"🔍 SKIPPING API CALL - Entity {i}: OwnerId={owner_id}, EntityName={entity_name} (already present)")
                            
                            # Always use the new format
                            normalized_entity = {
                                "entityId": int(entity_id) if entity_id else None,
                                "entityType": entity_type,
                                "ownerId": int(owner_id) if owner_id else None,
                                "entityName": entity_name
                            }
                            normalized_entity_details.append(normalized_entity)
                            logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")
                        # Publish final response event with proper format
                        await conversation_event_publisher.publish_conversation_completion(
                            chatbot_conversation_id=conversation_id,
                            completion_message=final_response,
                            charge=0,
                            chatbot_type=state.get("chatbotType", "AI"),
                            tenant_id=state.get("tenant_id"),
                            entity_details=normalized_entity_details,
                            message_conversation_id=state.get("message_conversation_id"),
                            chatbot_id=state.get("chatbot_id"),
                            chatbot_name=state.get("chatbot_name")
                        )
                        
                        # Publish workflow completion event if this was a workflow-triggered conversation
                        await self._publish_workflow_completion_if_needed(conversation_id, state, db)
                        return
                    else:
                        # Regular question (1-4), add follow-up
                        follow_up_options = [
                            "Is there anything else I can help you with?",
                            "Do you have any other questions?",
                            "What else would you like to know?",
                            "Is there anything else you'd like to ask about?"
                        ]

                        import random
                        follow_up = random.choice(follow_up_options)
                        complete_response = f"{ai_response}\n\n{follow_up}"

                        # Store conversation turn
                        prompt = [{"role": "user", "content": user_message}]
                        store_conversation_turn(
                            db, conversation_id, tenant_id,
                            llm_prompt=prompt,
                            llm_response=complete_response,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens
                        )

                        # Update state
                        state["history"].append({"role": "assistant", "content": complete_response})
                        redis_service = RedisService()
                        redis_service.store_conversation_state(conversation_id, state)

                        # Update conversation in database
                        update_conversation_in_db(db, conversation_id, state)

                        # 🔄 Trigger entity updates for this question
                        logger.info("🔄 Triggering entity updates for knowledge phase question")
                        entity_details = state.get("entity_details", [])
                        collected_answers = state.get("answers", [])

                        if entity_details and collected_answers:
                            try:
                                update_results = await chatbot_service.update_entities_after_conversation(
                                    entity_details,
                                    collected_answers,
                                    int(tenant_id),
                                    state.get("user_id", "unknown"),
                                    state.get("chatbot_id"),
                                    state  # Pass conversation state
                                )
                                logger.info(f"✅ Entity updates completed for knowledge question: {update_results}")
                            except Exception as e:
                                logger.error(f"❌ Entity update failed for knowledge question: {str(e)}")
                        else:
                            logger.warning("⚠️ No entities or answers to update for knowledge question")

                        # Publish knowledge response event
                        await conversation_event_publisher.publish_conversation_response(
                            chatbot_conversation_id=conversation_id,
                            message=complete_response,
                            completed=False,  # Still in knowledge phase, not completed until user indicates they're done
                            charge=0,  # No charge for knowledge responses
                            extra={"chatbotType": state.get("chatbotType")}
                        )
                        return

            else:
                # Chatbot has no knowledgebase configured
                logger.info(f"📝 Chatbot has no knowledgebase, but adding user question to position 6: '{user_message}'")
                question_result = chatbot_service.add_user_question_to_position_6(
                    state["chatbot_id"],
                    int(tenant_id),
                    user_message
                )
                logger.info(f"📝 Question addition result: {question_result}")
                # Also add question to conversation state for entity updates
                logger.info(f"📝 Adding user question to conversation state (no knowledgebase): '{user_message}'")
                try:
                    conversation_question_result = chatbot_service.add_question_to_conversation(
                        state,
                        user_message
                    )
                    logger.info(f"📝 Conversation question addition result: {conversation_question_result}")
                except Exception as e:
                    logger.error(f"❌ Error adding question to conversation state (no knowledgebase): {str(e)}")

                # Generate a generic response since there's no knowledgebase
                generic_response = "I don't have specific information about that topic, but I'd be happy to help with other questions you might have. Is there anything else I can assist you with?"

                # Store conversation turn
                prompt = [{"role": "user", "content": user_message}]
                store_conversation_turn(
                    db, conversation_id, tenant_id,
                    llm_prompt=prompt,
                    llm_response=generic_response,
                    input_tokens=0,
                    output_tokens=0
                )

                # Update state
                state["history"].append({"role": "assistant", "content": generic_response})
                redis_service = RedisService()
                redis_service.store_conversation_state(conversation_id, state)

                # Update conversation in database
                update_conversation_in_db(db, conversation_id, state)

                # 🔄 Trigger entity updates for this question (no knowledgebase)
                logger.info("🔄 Triggering entity updates for knowledge phase question (no knowledgebase)")
                entity_details = state.get("entity_details", [])
                collected_answers = state.get("answers", [])

                if entity_details and collected_answers:
                    try:
                        update_results = await chatbot_service.update_entities_after_conversation(
                            entity_details,
                            collected_answers,
                            int(tenant_id),
                            state.get("user_id", "unknown"),
                            state.get("chatbot_id"),
                            state  # Pass conversation state
                        )
                        logger.info(f"✅ Entity updates completed for knowledge question (no knowledgebase): {update_results}")
                    except Exception as e:
                        logger.error(f"❌ Entity update failed for knowledge question (no knowledgebase): {str(e)}")
                else:
                    logger.warning("⚠️ No entities or answers to update for knowledge question (no knowledgebase)")

                # Publish generic response event
                await conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=generic_response,
                    completed=False,  # Still in knowledge phase, not completed until user indicates they're done
                    charge=0,
                    extra=self._get_event_extra_data(state)
                )
                return

            # No answer found in knowledgebase
            base_response = "I don't have specific information about that."
            follow_up_options = [
                "Is there anything else I can help you with?",
                "Do you have any other questions?",
                "What else would you like to know?",
                "Is there anything else you'd like to ask about?"
            ]

            import random
            follow_up = random.choice(follow_up_options)
            ai_response = f"{base_response} {follow_up}"

            # Add user question to position 6 field even when no answer found
            logger.info(f"📝 Adding user question to position 6 field (no answer found): '{user_message}'")
            try:
                question_result = chatbot_service.add_user_question_to_position_6(
                    state["chatbot_id"],
                    int(tenant_id),
                    user_message
                )
                logger.info(f"📝 Question addition result: {question_result}")
            except Exception as e:
                logger.error(f"❌ Error adding question to position 6 (no answer): {str(e)}")
            
            # Also add question to conversation state for entity updates (no answer found)
            logger.info(f"📝 Adding user question to conversation state (no answer found): '{user_message}'")
            try:
                conversation_question_result = chatbot_service.add_question_to_conversation(
                    state,
                    user_message
                )
                logger.info(f"📝 Conversation question addition result: {conversation_question_result}")
            except Exception as e:
                logger.error(f"❌ Error adding question to conversation state (no answer): {str(e)}")

            # Store conversation turn
            no_info_prompt = [{"role": "user", "content": user_message}]
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=no_info_prompt,
                llm_response=ai_response,
                input_tokens=0,
                output_tokens=0
            )

            # Update state
            state["history"].append({"role": "assistant", "content": ai_response})
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            # 🔄 Trigger entity updates for this question (even when no answer found)
            logger.info("🔄 Triggering entity updates for knowledge phase question (no answer found)")
            entity_details = state.get("entity_details", [])
            collected_answers = state.get("answers", [])

            if entity_details and collected_answers:
                try:
                    update_results = await chatbot_service.update_entities_after_conversation(
                        entity_details,
                        collected_answers,
                        int(tenant_id),
                        state.get("user_id", "unknown"),
                        state.get("chatbot_id"),
                        state  # Pass conversation state
                    )
                    logger.info(f"✅ Entity updates completed for knowledge question (no answer): {update_results}")
                except Exception as e:
                    logger.error(f"❌ Entity update failed for knowledge question (no answer): {str(e)}")
            else:
                logger.warning("⚠️ No entities or answers to update for knowledge question (no answer)")

            # Publish no-info response event
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=ai_response,
                completed=False,  # Still in knowledge phase, not completed until user indicates they're done
                charge=0,
                extra={"chatbotType": state.get("chatbotType")}
            )

        except Exception as e:
            logger.error(f"Error handling knowledge phase: {str(e)}")
            raise

    async def _handle_question_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        user_message: str,
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle conversation in question-answer phase
        """
        try:
            logger.info("Processing message in question phase")

            # Check if the message content is valid (not None, empty, whitespace, or image-related content)
            def is_valid_text_answer(message_content):
                """Check if the message content is a valid text answer"""
                if not message_content or not message_content.strip():
                    return False
                
                # Check for common image-related patterns
                message_lower = message_content.lower().strip()
                
                # Common image file extensions
                image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif']
                if any(message_lower.endswith(ext) for ext in image_extensions):
                    return False
                
                # Check for base64 image patterns
                if message_lower.startswith('data:image/') or 'base64' in message_lower:
                    return False
                
                # Allow common short answers that are clearly valid responses
                common_short_answers = ['yes', 'no', 'ok', 'okay', 'yeah', 'nope', 'nah', 'sure', 'maybe']
                if message_lower in common_short_answers:
                    return True
                
                # Check for very short content that might be image metadata
                # Allow emojis and single characters that are clearly not image-related
                if len(message_content.strip()) < 3:
                    # Check if it's an emoji or special character
                    if len(message_content.strip()) == 1:
                        # Single characters are allowed (emojis, punctuation, etc.)
                        pass
                    else:
                        # Very short content (2 characters) might be image metadata
                        # But we already checked for common short answers above
                        return False
                
                # Check for common image-related keywords (only if they are standalone or very short)
                image_keywords = ['image', 'photo', 'picture', 'img', 'pic', 'upload', 'file', 'attachment']
                if any(keyword == message_lower for keyword in image_keywords):
                    return False
                
                # Check if content looks like a file path or URL
                if '/' in message_content or '\\' in message_content or 'http' in message_lower:
                    return False
                
                return True
            
            if not is_valid_text_answer(user_message):
                # User provided invalid content, ask the previous question again
                logger.info(f"📸 User provided invalid content in question phase")
                logger.info(f"📸 Invalid message content: '{user_message}'")
                
                if state.get("asked_questions"):
                    current_question = state["asked_questions"][-1]  # Last asked question
                    
                    # Create a response asking for the same question again
                    repeat_question_response = f"I received invalid content. Please provide a text answer for: {current_question['question']}"
                    
                    # Update state with the repeat question
                    state["history"].append({"role": "assistant", "content": repeat_question_response})
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)
                    
                    # Update conversation in database
                    update_conversation_in_db(db, conversation_id, state)
                    
                    # Publish the repeat question event
                    await self.conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=repeat_question_response,
                        completed=False,
                        charge=0,
                        extra={"chatbotType": state.get("chatbotType")}
                    )
                    return
                else:
                    logger.warning("No asked questions found in state for image upload handling")
                    return

            # Store the user's answer for the current question
            if state.get("asked_questions"):
                current_question = state["asked_questions"][-1]  # Last asked question
                new_answers = []

                # Handle multiple entity fields per question (new format)
                if "entity_fields" in current_question:
                    # New format: question maps to multiple entity fields
                    for entity_field in current_question["entity_fields"]:
                        answer_entry = {
                            "question_id": current_question["id"],
                            "question": current_question["question"],
                            "answer": user_message,
                            "field_name": entity_field["name"],  # Field name for entity update
                            "entity_type": entity_field["entity_type"]  # Entity type this question applies to
                        }

                        if "answers" not in state:
                            state["answers"] = []
                        state["answers"].append(answer_entry)
                        new_answers.append(answer_entry)
                else:
                    # Fallback for old format (should not happen with new questions)
                    answer_entry = {
                        "question_id": current_question["id"],
                        "question": current_question["question"],
                        "answer": user_message,
                        "field_name": current_question.get("field_name"),
                        "entity_type": current_question.get("entity_type")
                    }

                    if "answers" not in state:
                        state["answers"] = []
                    state["answers"].append(answer_entry)
                    new_answers.append(answer_entry)

                # Track credit usage for this question-answer interaction
                chatbot_service = ChatbotService()
                has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], int(tenant_id))

                track_credit_usage(
                    db=db,
                    chatbot_id=state["chatbot_id"],
                    conversation_id=conversation_id,
                    tenant_id=tenant_id,
                    question=current_question["question"],
                    answer=user_message,
                    has_knowledgebase=has_knowledgebase
                )

                # Trigger per-answer entity update using only newly added answers
                try:
                    entity_details = state.get("entity_details", [])
                    logger.info(f"🔍 AI DEBUG: entity_details count: {len(entity_details) if entity_details else 0}")
                    logger.info(f"🔍 AI DEBUG: new_answers count: {len(new_answers) if new_answers else 0}")
                    logger.info(f"🔍 AI DEBUG: new_answers content: {new_answers}")
                    
                    if entity_details and new_answers:
                        logger.info("🔄 Triggering per-answer entity update")
                        logger.info(f"🔍 AI DEBUG: tenant_id: {tenant_id}")
                        logger.info(f"🔍 AI DEBUG: chatbot_id: {state.get('chatbot_id')}")
                        logger.info(f"🔍 AI DEBUG: user_id (raw): {state.get('user_id')}")
                        
                        # Ensure numeric user_id for event metadata
                        user_id_safe = state.get("user_id")
                        try:
                            user_id_safe = int(user_id_safe)
                            logger.info(f"🔍 AI DEBUG: user_id (converted): {user_id_safe}")
                        except Exception as e:
                            user_id_safe = 0
                            logger.warning(f"⚠️ AI: Failed to convert user_id to int: {e}, using 0")
                        
                        logger.info(f"🔍 AI DEBUG: Calling update_entities_after_conversation with {len(new_answers)} answers")
                        update_results = await chatbot_service.update_entities_after_conversation(
                            entity_details,
                            new_answers,
                            int(tenant_id),
                            user_id_safe,
                            state.get("chatbot_id"),
                            state
                        )
                        logger.info(f"✅ Per-answer entity update done: {update_results}")
                    else:
                        if not entity_details:
                            logger.warning("⚠️ Skipping per-answer update: no entity_details")
                        if not new_answers:
                            logger.warning("⚠️ Skipping per-answer update: no new answers")
                except Exception as e:
                    logger.error(f"❌ Per-answer entity update failed: {str(e)}")

            # Check if we have more questions remaining
            if state.get("remaining_questions"):
                # Use LLM to select the next question
                next_question, input_tokens, output_tokens, model = es_service.select_next_question(
                    state["history"],
                    state["remaining_questions"],
                    state["answers"]
                )

                if next_question:
                    # Move the selected question from remaining to asked
                    state["remaining_questions"] = [q for q in state["remaining_questions"] if q["id"] != next_question["id"]]
                    state["asked_questions"].append(next_question)

                    # Create a prompt for OpenAI to generate a natural transition
                    prompt = [
                        {"role": "system", "content": "You are a helpful assistant collecting information from users. Based on the conversation history, acknowledge the user's response and ask the next question in a natural way."},
                        {"role": "user", "content": f"Conversation history: {json.dumps(state['answers'])}\n\nNext question to ask: {next_question['question']}\n\nGenerate a natural response that acknowledges what the user just said and then asks the next question."}
                    ]

                    logger.info(f"🧠 AI FLOW - Making OpenAI API call for question transition in conversation {conversation_id}")
                    ai_response, transition_input_tokens, transition_output_tokens, transition_model = es_service.generate_chat_response(prompt, max_tokens=150)

                    # Store conversation turn
                    store_conversation_turn(
                        db, conversation_id, tenant_id,
                        llm_prompt=prompt,
                        llm_response=ai_response,
                        input_tokens=transition_input_tokens,
                        output_tokens=transition_output_tokens
                    )

                    # Update state
                    state["history"].append({"role": "assistant", "content": ai_response})
                    redis_service = RedisService()
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database
                    update_conversation_in_db(db, conversation_id, state)

                    # Calculate charge for the next question
                    charge = charge_calculator.calculate_question_charge(
                        next_question, is_predefined=True, is_llm_generated=False
                    )

                    # Publish next question event
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=ai_response,
                        completed=False,
                        charge=charge,
                        extra={"chatbotType": state.get("chatbotType")}
                    )
                    return

            # All predefined questions answered - skip custom questions and go directly to completion
            logger.info("All predefined questions completed. Skipping custom questions phase and proceeding to completion.")
            
            # Mark custom questions as already processed to prevent any custom question generation
            state["custom_questions_generated"] = True
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)
            
            # Proceed directly to completion phase
            await self._handle_completion_phase(
                db, state, conversation_id, tenant_id, es_service
            )

        except Exception as e:
            logger.error(f"Error handling question phase: {str(e)}")
            raise

    async def _handle_custom_questions_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle generation and asking of custom follow-up questions based on user responses
        """
        try:
            logger.info("Checking if custom follow-up questions are needed")

            # Check if we've already generated custom questions for this conversation
            if state.get("custom_questions_generated", False):
                # Custom questions already processed, proceed to completion
                await self._handle_completion_phase(
                    db, state, conversation_id, tenant_id, es_service
                )
                return

            # Generate custom follow-up questions based on user responses
            custom_questions = await self._generate_custom_questions(
                state.get("answers", []), es_service
            )

            if custom_questions:
                # Add custom questions to remaining questions
                state["remaining_questions"].extend(custom_questions)
                state["custom_questions_generated"] = True

                # Store updated state
                redis_service = RedisService()
                redis_service.store_conversation_state(conversation_id, state)

                # Select and ask the first custom question
                next_question, input_tokens, output_tokens, model = es_service.select_next_question(
                    state["history"],
                    state["remaining_questions"],
                    state["answers"]
                )

                if next_question:
                    # Move the selected question from remaining to asked
                    state["remaining_questions"] = [q for q in state["remaining_questions"] if q["id"] != next_question["id"]]
                    state["asked_questions"].append(next_question)

                    # Create a prompt for OpenAI to generate a natural transition
                    prompt = [
                        {"role": "system", "content": "You are a helpful assistant collecting information from users. Based on the conversation history, acknowledge the user's response and ask a follow-up question in a natural way."},
                        {"role": "user", "content": f"Conversation history: {json.dumps(state['answers'])}\n\nFollow-up question to ask: {next_question['question']}\n\nGenerate a natural response that acknowledges what the user has shared and then asks the follow-up question."}
                    ]

                    ai_response, transition_input_tokens, transition_output_tokens, transition_model = es_service.generate_chat_response(prompt, max_tokens=150)

                    # Store conversation turn
                    store_conversation_turn(
                        db, conversation_id, tenant_id,
                        llm_prompt=prompt,
                        llm_response=ai_response,
                        input_tokens=transition_input_tokens,
                        output_tokens=transition_output_tokens
                    )

                    # Update state
                    state["history"].append({"role": "assistant", "content": ai_response})
                    redis_service.store_conversation_state(conversation_id, state)

                    # Update conversation in database
                    update_conversation_in_db(db, conversation_id, state)

                    # Calculate charge for the custom question
                    charge = charge_calculator.calculate_question_charge(
                        next_question, is_predefined=False, is_llm_generated=True
                    )

                    # Publish next question event
                    await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=ai_response,
                        completed=False,
                        charge=charge,
                        extra={"chatbotType": state.get("chatbotType")}
                    )
                    return

            # No custom questions needed or generated, proceed to completion
            state["custom_questions_generated"] = True
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)

            await self._handle_completion_phase(
                db, state, conversation_id, tenant_id, es_service
            )

        except Exception as e:
            logger.error(f"Error handling custom questions phase: {str(e)}")
            # Fallback to completion phase
            await self._handle_completion_phase(
                db, state, conversation_id, tenant_id, es_service
            )

    async def _generate_custom_questions(
        self,
        answers: List[Dict[str, Any]],
        es_service: ElasticsearchService
    ) -> List[Dict[str, Any]]:
        """
        Generate custom follow-up questions based on user responses
        """
        try:
            if not answers:
                return []

            # Create context from answered questions
            answered_context = ""
            for qa in answers:
                answered_context += f"Q: {qa.get('question', '')}\nA: {qa.get('answer', '')}\n\n"

            # Create a prompt for generating follow-up questions
            prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful assistant that generates relevant follow-up questions based on user responses.

Your task is to:
1. Analyze the user's answers to identify areas that need clarification or could benefit from more detail
2. Generate 1-2 relevant follow-up questions that would help gather more useful information
3. Only generate questions if the answers suggest there's valuable follow-up information to collect
4. Focus on questions that would help better understand the user's needs or situation

Respond with a JSON array of questions in this format:
[
  {"question": "Follow-up question 1"},
  {"question": "Follow-up question 2"}
]

If no follow-up questions are needed, respond with an empty array: []"""
                },
                {
                    "role": "user",
                    "content": f"""Based on these answered questions, determine if any follow-up questions would be valuable:

{answered_context}

Generate relevant follow-up questions (maximum 2) or return empty array if none are needed."""
                }
            ]

            response_text, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=300)

            # Parse the JSON response
            import json
            import uuid
            try:
                questions_data = json.loads(response_text.strip())
                if not isinstance(questions_data, list):
                    return []

                custom_questions = []
                for i, q_data in enumerate(questions_data[:2]):  # Limit to 2 questions
                    if isinstance(q_data, dict) and "question" in q_data:
                        custom_questions.append({
                            "id": str(uuid.uuid4()),
                            "question": q_data["question"],
                            "is_llm_generated": True,
                            "is_predefined": False,
                            "field_name": None,
                            "entity_type": None
                        })

                logger.info(f"Generated {len(custom_questions)} custom follow-up questions")
                return custom_questions

            except json.JSONDecodeError:
                logger.warning(f"Could not parse custom questions response: {response_text}")
                return []

        except Exception as e:
            logger.error(f"Error generating custom questions: {str(e)}")
            return []

    async def _handle_completion_phase(
        self,
        db: Session,
        state: Dict[str, Any],
        conversation_id: str,
        tenant_id: str,
        es_service: ElasticsearchService
    ):
        """
        Handle conversation completion phase
        """
        try:
            logger.info("Processing conversation completion")

            # Check if chatbot has knowledgebase to determine conversation flow
            chatbot_service = ChatbotService()
            has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], int(tenant_id))
            
            logger.info(f"🔍 COMPLETION PHASE - Chatbot ID: {state['chatbot_id']}, Tenant ID: {tenant_id}, Has Knowledgebase: {has_knowledgebase}")

            if has_knowledgebase:
                # Transition to knowledge search phase
                prompt = [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant. The user has completed answering all the required questions. Now transition them to a knowledge search phase where they can ask questions about the topic."
                    },
                    {
                        "role": "user",
                        "content": f"""The user has completed answering these questions:
                        {json.dumps([ans.get('question', '') for ans in state.get('answers', [])])}

                        Generate a natural transition message that:
                        1. Thanks them for providing the information
                        2. Lets them know they can now ask questions about the topic
                        3. Encourages them to ask anything they'd like to know"""
                    }
                ]

                transition_message, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=150)
                completed = True  # Mark as completed but not ended (knowledge phase)
            else:
                # No knowledgebase - end conversation with thank you message
                chatbot = db.query(ChatbotConversation).filter(
                    ChatbotConversation.id == conversation_id
                ).first()

                thank_you_message = "Thank you for providing all the information!"
                if chatbot and hasattr(chatbot, 'thank_you_message') and chatbot.thank_you_message:
                    thank_you_message = chatbot.thank_you_message

                prompt = [
                    {
                        "role": "user",
                        "content": f"""Please enhance this thank you message for the end of conversation:

                        Original message: "{thank_you_message}"

                        Context: The user has completed all questions and there's no knowledgebase for further assistance, so this ends the conversation."""
                    }
                ]

                transition_message, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=100)
                completed = True

            # Store conversation turn
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=prompt,
                llm_response=transition_message,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

            # 🔄 Trigger entity updates (predefined questions completion)
            logger.info("🔄 Starting entity update process after predefined questions completion")
            entity_details = state.get("entity_details", [])
            collected_answers = state.get("answers", [])

            logger.info(f"📊 Entities to update: {len(entity_details)}")
            for i, entity in enumerate(entity_details, 1):
                logger.info(f"   Entity {i}: {entity.get('entityType', 'unknown')} (ID: {entity.get('id', 'unknown')})")

            logger.info(f"📝 Total answers collected: {len(collected_answers)}")
            for i, answer in enumerate(collected_answers, 1):
                logger.info(f"   Answer {i}: '{answer.get('question', 'unknown')}' = '{answer.get('answer', 'unknown')}'")

            if entity_details and collected_answers:
                try:
                    logger.info("🚀 Calling update_entities_after_conversation...")
                    # Create ChatbotService instance for entity updates
                    entity_update_service = ChatbotService()
                    update_results = await entity_update_service.update_entities_after_conversation(
                        entity_details,
                        collected_answers,
                        tenant_id,
                        state.get("user_id", "unknown"),
                        state.get("chatbot_id"),
                        state  # Pass conversation state
                    )
                    logger.info(f"✅ Entity updates completed successfully: {update_results}")
                except Exception as e:
                    logger.error(f"❌ Entity update failed: {str(e)}")
            else:
                logger.warning("⚠️ No entities or answers to update - skipping entity updates")
                if not entity_details:
                    logger.warning("   - entity_details is empty or None")
                if not collected_answers:
                    logger.warning("   - collected_answers is empty or None")

            # Update state to indicate transition to knowledge phase (but not completed yet)
            if has_knowledgebase:
                state["completed"] = True  # Transition to knowledge phase
                state["questions_completed"] = True  # Mark questions as done
            else:
                state["completed"] = True  # End conversation immediately
                state["ended"] = True

            state["history"].append({"role": "assistant", "content": transition_message})
            redis_service = RedisService()
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database - do NOT mark as completed yet if has knowledgebase
            if has_knowledgebase:
                logger.info(f"🔄 COMPLETION PHASE - Has knowledgebase, marking as completed=False (knowledge phase)")
                update_conversation_in_db(db, conversation_id, state, completed=False)  # Not completed yet - waiting for user to indicate they're done
            else:
                logger.info(f"🔄 COMPLETION PHASE - No knowledgebase, marking as completed=True (immediate completion)")
                update_conversation_in_db(db, conversation_id, state, completed=True)  # Complete immediately

            # Publish transition event (NOT completion yet)
            if has_knowledgebase:
                # Transition to knowledge phase - do NOT mark as completed yet
                await conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=transition_message,
                    completed=False,  # NOT completed yet - will be completed when user indicates they're done
                    charge=0,
                    extra=self._get_event_extra_data(state)
                )
            else:
                # Normalize and enrich entity details for immediate completion
                normalized_entity_details_immediate = []
                entity_details_immediate = state.get("entity_details", [])
                auth_token = state.get("auth_token") or "dummy_token"
                
                logger.info(f"🔍 AI CHATBOT COMPLETION DEBUG - Entity details count: {len(entity_details_immediate)}")
                logger.info(f"🔍 AI CHATBOT COMPLETION DEBUG - Auth token available: {bool(auth_token and auth_token != 'dummy_token')}")
                logger.info(f"🔍 AI CHATBOT COMPLETION DEBUG - Auth token length: {len(auth_token) if auth_token else 0}")
                
                for i, entity in enumerate(entity_details_immediate, 1):
                    # Support both old and new formats
                    entity_id = entity.get("id") or entity.get("entityId")
                    entity_type = (entity.get("entityType") or entity.get("entity") or "").upper()
                    owner_id = entity.get("ownerId")
                    entity_name = entity.get("name") or entity.get("entityName")
                    
                    logger.info(f"🔍 ENTITY {i} DEBUG - ID: {entity_id}, Type: {entity_type}, OwnerId: {owner_id}, EntityName: {entity_name}")
                    
                    # If ownerId or entityName is missing, fetch from API
                    if (not owner_id or not entity_name) and entity_id and entity_type:
                        logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                        try:
                            entity_field_service = EntityFieldService()
                            entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                            fetched_owner_id = entity_info.get("ownerId")
                            fetched_entity_name = entity_info.get("entityName")
                            
                            logger.info(f"🔍 API RESPONSE - Entity {i}: OwnerId={fetched_owner_id}, EntityName={fetched_entity_name}")
                            
                            if fetched_owner_id:
                                owner_id = fetched_owner_id
                                logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                            else:
                                logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                # Keep existing owner_id if API call failed
                            
                            if fetched_entity_name:
                                entity_name = fetched_entity_name
                                logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                            else:
                                logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                # Keep existing entity_name if API call failed
                                
                        except Exception as e:
                            logger.error(f"❌ Error fetching entity details for entity {i}: {str(e)}")
                            logger.warning(f"⚠️ API ENRICHMENT FAILED - Entity {i}: Keeping existing values (ownerId={owner_id}, entityName={entity_name})")
                    else:
                        logger.info(f"🔍 SKIPPING API CALL - Entity {i}: OwnerId={owner_id}, EntityName={entity_name} (already present)")
                    
                    # Always use the new format
                    normalized_entity = {
                        "entityId": int(entity_id) if entity_id else None,
                        "entityType": entity_type,
                        "ownerId": int(owner_id) if owner_id else None,
                        "entityName": entity_name
                    }
                    normalized_entity_details_immediate.append(normalized_entity)
                    logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")
                # End conversation immediately if no knowledgebase with proper format
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message=transition_message,
                    charge=0,
                    chatbot_type=state.get("chatbotType", "AI"),
                    tenant_id=state.get("tenant_id"),
                    entity_details=normalized_entity_details_immediate,
                    message_conversation_id=state.get("message_conversation_id"),
                    chatbot_id=state.get("chatbot_id"),
                    chatbot_name=state.get("chatbot_name")
                )
                
                # Publish workflow completion event if this was a workflow-triggered conversation
                await self._publish_workflow_completion_if_needed(conversation_id, state, db)

        except Exception as e:
            logger.error(f"Error handling completion phase: {str(e)}")
            raise

    async def _publish_workflow_completion_if_needed(
        self,
        conversation_id: str,
        state: Dict[str, Any],
        db: Session
    ):
        """
        Publish workflow completion event if this conversation was started by a workflow.
        
        Args:
            conversation_id: The conversation ID
            state: Conversation state containing workflow reply headers
            db: Database session for entity update queries
        """
        try:
            # Check if this conversation has workflow reply headers
            workflow_reply_headers = state.get("workflow_reply_headers")
            if not workflow_reply_headers:
                logger.debug(f"No workflow reply headers found for conversation {conversation_id}, skipping workflow completion event")
                return

            # Import here to avoid circular imports
            from app.services.workflow_event_listener import workflow_event_listener
            
            # For automatic completions, we'll provide summary stats
            execution_details = {
                "totalEntities": len(state.get("entity_details", [])),
                "completionType": "automatic",
                "completionMethod": "conversation_flow_end"
            }
            
            # Try to get more specific entity update stats if available in state
            if "entity_update_results" in state:
                update_results = state["entity_update_results"]
                execution_details.update({
                    "entitiesUpdated": update_results.get("successful_updates", 0),
                    "entitiesFailed": update_results.get("failed_updates", 0),
                    "totalEntities": update_results.get("total_entities", len(state.get("entity_details", [])))
                })

            await workflow_event_listener.publish_workflow_completion_success(
                conversation_id=conversation_id,
                message_conversation_id=state.get("message_conversation_id"),
                chatbot_id=state.get("chatbot_id"),
                tenant_id=state.get("tenant_id"),
                reply_headers=workflow_reply_headers,
                execution_details=execution_details
            )
            logger.info(f"Published workflow completion success event for {conversation_id}")
            
        except Exception as e:
            logger.error(f"Error publishing workflow completion success event for {conversation_id}: {str(e)}")
            # Don't fail the conversation completion if workflow event publishing fails

    async def _collect_chained_nodes(
        self,
        db: Session,
        state: Dict[str, Any],
        current_node,
        field_values: Dict[str, Any],
        entity_field_service: EntityFieldService
    ) -> tuple[List[Dict[str, Any]], bool]:
        """
        Collect all chained nodes (sendMessage, question, buttons, and list) that should be sent together.
        
        This method recursively traverses the chain and collects all nodes that don't require
        user input, building their nodeDetails for a single batch publication. Buttons and list nodes
        are included in the chain but stop further chaining as they require user interaction.
        
        Args:
            db: Database session
            state: Conversation state
            current_node: The current node to start chaining from
            field_values: Field values for variable substitution
            entity_field_service: Entity field service instance
            
        Returns:
            tuple: (List of nodeDetails for all chained nodes, bool indicating if chain ends at final node)
        """
        chained_nodes = []
        current = current_node
        visited_nodes = set()  # Track visited nodes to detect loops
        max_chain_length = 10  # Maximum allowed chain length to prevent infinite loops
        chain_ends_at_final_node = False  # Track if chain ends at a node with no outgoing edges
        
        try:
            chain_length = 0
            while current and chain_length < max_chain_length:
                chain_length += 1
                current_node_id = current.node_id
                
                logger.info(f"🔗 COLLECTING CHAIN - Processing node {current_node_id} (chain length: {chain_length})")
                
                # Check for loop detection
                if current_node_id in visited_nodes:
                    logger.error(f"🔄 LOOP DETECTED - Node {current_node_id} already visited in this chain! Breaking to prevent infinite loop.")
                    logger.error(f"🔄 LOOP DETECTED - Visited nodes: {visited_nodes}")
                    logger.error(f"🔄 LOOP DETECTED - Current chain: {[node['id'] for node in chained_nodes]}")
                    break
                
                # Add current node to visited set
                visited_nodes.add(current_node_id)
                
                # Get outgoing edges from the current node
                outgoing_edges = self._get_outgoing_edges(db, state, current.node_id)

                if not outgoing_edges:
                    logger.info(f"🔗 COLLECTING CHAIN - No outgoing edges from {current_node_id}, chain ends")
                    logger.info(f"🏁 CHAIN COMPLETION - Node {current_node_id} has no outgoing edges, this will trigger conversation completion")
                    chain_ends_at_final_node = True
                    break
                
                # For chaining, we'll take the first outgoing edge (no user input needed)
                next_edge = outgoing_edges[0]
                next_node_id = next_edge.target_node
                
                logger.info(f"🔗 COLLECTING CHAIN - Found next edge to node {next_node_id}")
                
                # Load the next node
                next_node = self._load_node(db, state, next_node_id)
                if not next_node:
                    logger.warning(f"🔗 COLLECTING CHAIN - Next node {next_node_id} not found")
                    break
                
                # Check if the next node can be chained
                next_node_type = (getattr(next_node, "type", "") or "").strip()
                if next_node_type not in ["sendMessage", "question", "buttons", "list"]:
                    logger.info(f"🔗 COLLECTING CHAIN - Next node {next_node_id} is {next_node_type}, cannot chain")
                    break
                
                # Build nodeDetails for the chained node
                node_details = self._build_node_details(next_node, field_values, entity_field_service, state, db)
                if node_details:
                    chained_nodes.append(node_details)
                    logger.info(f"🔗 COLLECTING CHAIN - Added node {next_node.node_id} to chain")
                    logger.info(f"🔗 COLLECTING CHAIN - Node details: {node_details}")
                else:
                    logger.error(f"🔗 COLLECTING CHAIN - Failed to build node details for {next_node.node_id}")
                
                # Check if we should stop chaining at question, buttons, or list nodes
                if next_node_type in ["question", "buttons", "list"]:
                    # Check if this node has outgoing edges
                    final_node_edges = self._get_outgoing_edges(db, state, next_node_id)
                    logger.info(f"🔍 START CHAIN DEBUG - Node {next_node_id} ({next_node_type}) has {len(final_node_edges)} outgoing edges")
                    
                    # Special handling for question nodes
                    should_break = True
                    if next_node_type == "question":
                        node_data = getattr(next_node, 'data', None) or {}
                        options = node_data.get('options') or []
                        logger.info(f"🔍 START CHAIN DEBUG - Question node {next_node_id} has {len(options)} options")
                        
                        # Question nodes ALWAYS require user input (either selecting option or typing free text)
                        # So we always stop the chain at question nodes (should_break = True)
                        if not options:
                            # Free-text question (no options)
                            if not final_node_edges:
                                # No options AND no edges → will complete after user provides answer
                                logger.info(f"🏁 START CHAIN COMPLETION - Question node {next_node_id} has NO OPTIONS and NO EDGES → Will complete after user provides free-text answer")
                                chain_ends_at_final_node = True
                            else:
                                # No options BUT has edges → will proceed after user provides free-text answer
                                logger.info(f"🔗 START CHAIN STOP - Question node {next_node_id} has NO OPTIONS but HAS {len(final_node_edges)} edges → Waiting for user's free-text answer before proceeding")
                        else:
                            # Multiple-choice question (has options)
                            if not final_node_edges:
                                logger.info(f"🏁 START CHAIN COMPLETION - Question node {next_node_id} has {len(options)} options but NO EDGES → Will complete after user selects option")
                                chain_ends_at_final_node = True
                            else:
                                logger.info(f"🔗 START CHAIN STOP - Question node {next_node_id} has {len(options)} options → Waiting for user to select option")
                        # Always stop chain at question nodes (user input required)
                        should_break = True
                    else:
                        # Buttons or list nodes always require user interaction
                        if not final_node_edges:
                            logger.info(f"🏁 START CHAIN COMPLETION - {next_node_type.title()} node {next_node_id} has NO EDGES → Will complete after user interaction")
                            chain_ends_at_final_node = True
                        else:
                            logger.info(f"🔗 START CHAIN STOP - {next_node_type.title()} node {next_node_id} requires user interaction → Stopping chain")
                        should_break = True
                    
                    if should_break:
                        break
                
                # Move to next node (only for sendMessage nodes)
                current = next_node
            
            # Check if we hit the maximum chain length
            if chain_length >= max_chain_length:
                logger.error(f"🔄 MAXIMUM CHAIN LENGTH REACHED - Chain length {chain_length} exceeds maximum {max_chain_length}. Breaking to prevent infinite loop.")
                logger.error(f"🔄 MAXIMUM CHAIN LENGTH - Visited nodes: {visited_nodes}")
                logger.error(f"🔄 MAXIMUM CHAIN LENGTH - Current chain: {[node['id'] for node in chained_nodes]}")
                    
        except Exception as e:
            logger.error(f"Error collecting chained nodes: {str(e)}")
            # Don't raise the exception to avoid breaking the main conversation flow
            logger.warning("Continuing conversation flow despite chaining error")
        
        logger.info(f"🔗 COLLECTING CHAIN - Collected {len(chained_nodes)} chained nodes, chain_ends_at_final_node: {chain_ends_at_final_node}")
        return chained_nodes, chain_ends_at_final_node
    
    def _build_node_details(self, node, field_values: Dict[str, Any], entity_field_service: EntityFieldService, state: Dict[str, Any] = None, db: Session = None) -> Dict[str, Any]:
        """
        Build nodeDetails for a given node with variable substitution.
        
        Args:
            node: The node to build details for
            field_values: Field values for variable substitution
            entity_field_service: Entity field service instance
            state: Conversation state (optional, for loading node-specific variable mappings)
            
        Returns:
            Dict[str, Any]: NodeDetails dictionary
        """
        try:
            # Use stored isFirstNode value from database
            is_first_node = getattr(node, 'is_first_node', False)
            
            node_details = {
                "id": node.node_id,
                "name": getattr(node, "name", None),
                "type": node.type,
                "isFirstNode": is_first_node
            }
            
            data_obj = (getattr(node, "data", None) or {})
            
            # Get variable mappings from this specific node
            # IMPORTANT: Each node's variable mappings are completely independent
            # Nodes do NOT inherit variable values from parent nodes
            node_field_values = {}
            
            if state:
                entity_details = state.get("entity_details", [])
                if entity_details:
                    node_mappings = []
                    # Check multiple possible locations for variable mappings
                    if hasattr(node, 'variable_mapping') and node.variable_mapping:
                        node_mappings = node.variable_mapping
                    elif hasattr(node, 'variableMapping') and node.variableMapping:
                        node_mappings = node.variableMapping
                    elif hasattr(node, 'data') and node.data:
                        node_mappings = node.data.get("variableMapping") or []
                    
                    if node_mappings:
                        # Get auth token from state for variable mapping
                        auth_token = state.get("auth_token") or "dummy_token"
                        allowed_entities = self._get_allowed_entities(db, state.get("chatbot_id"), state.get("tenant_id")) if db else None
                        node_specific_values = entity_field_service.get_entity_field_values_from_mappings(entity_details, node_mappings, auth_token, allowed_entities)
                        logger.info(f"🔗 NODE VARIABLE MAPPING - Node {node.node_id} has own mappings: {node_specific_values}")
                        node_field_values = node_specific_values  # Use ONLY this node's mappings
                    else:
                        # Node has no variable mappings - use empty dict (no inheritance)
                        node_field_values = {}
                        logger.info(f"🔗 NODE VARIABLE MAPPING - Node {node.node_id} has no mappings, using empty field values")
            
            # Apply variable substitution to node data
            substituted_data = entity_field_service.substitute_node_variables(data_obj, node_field_values)
            
            if node.type == "sendMessage":
                # Build blocks for sendMessage nodes
                blocks = []
                for opt in (substituted_data.get("options") or []):
                    if opt.get("type") == "text":
                        blocks.append({"type": "text", "text": opt.get("text")})
                    elif opt.get("type") == "media":
                        blocks.append({"type": "media", "mediaFile": opt.get("mediaFile")})
                node_details["data"] = blocks
                
            elif node.type == "question":
                # Build question data
                question_text = substituted_data.get("text") or ""
                options = substituted_data.get("options") or []
                
                if options:
                    lines = [question_text] if question_text else []
                    for idx, opt in enumerate(options, start=1):
                        label = opt.get("text") or opt.get("name") or str(idx)
                        lines.append(f"{idx}. {label}")
                    message_text = "\n".join(lines)
                else:
                    message_text = question_text
                
                node_details["data"] = {
                    "text": message_text,
                    "options": options
                }
            
            elif node.type == "list":
                # Build list data for WhatsApp list messages
                header = substituted_data.get("header") or ""
                body = substituted_data.get("body") or ""
                footer = substituted_data.get("footer") or ""
                menu_button = substituted_data.get("menuButton") or "View All"
                sections = substituted_data.get("sections") or []
                
                # Build sections for WhatsApp list format - preserve all fields
                list_sections = []
                for section in sections:
                    section_data = {
                        "title": section.get("title", ""),
                        "rows": section.get("rows", [])
                    }
                    # Preserve id and position if present
                    if "id" in section:
                        section_data["id"] = section["id"]
                    if "position" in section:
                        section_data["position"] = section["position"]
                    
                    if section_data["rows"]:  # Only add section if it has rows
                        list_sections.append(section_data)
                
                node_details["data"] = {
                    "header": header,
                    "body": body,
                    "footer": footer,
                    "menuButton": menu_button,
                    "sections": list_sections
                }
            
            elif node.type == "buttons":
                # Build button data
                header = substituted_data.get("header")
                body = substituted_data.get("body") or ""
                footer = substituted_data.get("footer") or ""
                buttons = substituted_data.get("buttons") or []
                
                # Handle header - convert string to object format if needed
                if isinstance(header, str):
                    header = {
                        "format": "text",
                        "text": header,
                        "mediaFile": None
                    }
                elif not header:
                    header = {
                        "format": "text",
                        "text": "",
                        "mediaFile": None
                    }
                elif isinstance(header, dict):
                    # Ensure mediaFile is present (can be None)
                    if "mediaFile" not in header:
                        header["mediaFile"] = None
                
                # Format buttons to ensure they have required fields
                formatted_buttons = []
                for btn in buttons:
                    button_id = btn.get("id") or btn.get("name") or btn.get("text", "")
                    formatted_btn = {
                        "id": button_id,
                        "name": button_id,
                        "text": btn.get("text", ""),
                        "position": btn.get("position", 0)
                    }
                    formatted_buttons.append(formatted_btn)
                
                node_details["data"] = {
                    "header": header,
                    "body": body,
                    "footer": footer,
                    "buttons": formatted_buttons
                }
            
            logger.info(f"🔗 BUILDING NODE DETAILS - Built for node {node.node_id}")
            return node_details
            
        except Exception as e:
            logger.error(f"Error building nodeDetails for node {node.node_id}: {str(e)}")
            return None
    
    async def _publish_chained_nodes(
        self,
        conversation_id: str,
        state: Dict[str, Any],
        chained_nodes: List[Dict[str, Any]]
    ):
        """
        Publish all chained nodes in a single event with nodeDetails array.
        
        Args:
            conversation_id: Conversation ID
            state: Conversation state
            chained_nodes: List of nodeDetails for chained nodes
        """
        try:
            if not chained_nodes:
                logger.info("🔗 PUBLISHING CHAIN - No chained nodes to publish")
                return
            
            # Update conversation state to point to the last node in the chain
            last_node_id = chained_nodes[-1]["id"]
            state["rule_current_node_id"] = last_node_id
            RedisService().store_conversation_state(conversation_id, state)
            
            # Publish all chained nodes in a single event
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=None,  # Chained nodes use nodeDetails array, not message
                completed=False,
                charge=0,
                message_conversation_id=state.get("messageConversationId"),
                extra={
                    "chatbotType": state.get("chatbotType"),
                    "nodeDetails": chained_nodes  # Array of nodeDetails
                }
            )
            
            logger.info(f"🔗 PUBLISHING CHAIN - Published {len(chained_nodes)} chained nodes in single event")
            
        except Exception as e:
            logger.error(f"Error publishing chained nodes: {str(e)}")
            # Don't raise the exception to avoid breaking the main conversation flow
            logger.warning("Continuing conversation flow despite chaining error")


    def _get_allowed_entities(self, db: Session, chatbot_id: str, tenant_id: int) -> Optional[List[str]]:
        """Get list of allowed entity types from chatbot configuration."""
        if not chatbot_id:
            return None
        try:
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            if not chatbot or not chatbot.entities:
                return None
            allowed_entities = [entity.get('entity', '').upper() for entity in chatbot.entities if entity.get('entity')]
            logger.info(f"ENTITY FILTER - Loaded {len(allowed_entities)} allowed entities: {allowed_entities}")
            return allowed_entities
        except Exception as e:
            logger.error(f"ENTITY FILTER - Error: {str(e)}")
            return None

# Global instance
message_event_listener = MessageEventListener()
