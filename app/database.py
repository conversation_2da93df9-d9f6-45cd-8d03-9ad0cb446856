from sqlalchemy import create_engine, Column, String, Integer, ForeignKey, Text, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import OperationalError, DisconnectionError
import os
from dotenv import load_dotenv
import logging
import time
from contextlib import contextmanager

# Load environment variables from .env file
load_dotenv()

# Get logger
logger = logging.getLogger(__name__)

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "user")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "test")
DB_HOST = os.getenv("POSTGRES_HOST", "postgresql-postgresql")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "whatsapp_chatbot")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
logger.info(f"Connecting to database and url is {DATABASE_URL}", extra={"db_host": DB_HOST, "db_name": DB_NAME, "db_user": DB_USER})

def get_database_url():
    """Get the database URL for use in migrations and other contexts"""
    return DATABASE_URL

# Print the database URL (with password masked for security)
masked_url = f"postgresql://{DB_USER}:****@{DB_HOST}:{DB_PORT}/{DB_NAME}"
logger.info(f"Connecting to database", extra={"db_host": DB_HOST, "db_name": DB_NAME, "db_user": DB_USER})

# Enhanced engine configuration with connection pooling and retry logic
engine = create_engine(
    DATABASE_URL,
    # Connection pooling configuration
    poolclass=QueuePool,
    pool_size=20,  # Number of connections to maintain
    max_overflow=30,  # Additional connections that can be created
    pool_pre_ping=True,  # Validate connections before use
    pool_recycle=3600,  # Recycle connections after 1 hour
    pool_timeout=30,  # Timeout for getting connection from pool
    # Connection configuration
    connect_args={
        "connect_timeout": 10,  # Connection timeout
        "application_name": "whatsapp_chatbot",  # Application name for monitoring
        "options": "-c statement_timeout=30000"  # 30 second statement timeout
    },
    # Engine configuration
    echo=False,  # Set to True for SQL logging
    echo_pool=False,  # Set to True for pool logging
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """Get database session with retry logic"""
    max_retries = 3
    retry_delay = 1  # seconds
    
    for attempt in range(max_retries):
        db = None
        try:
            db = SessionLocal()
            # Test the connection
            db.execute(text("SELECT 1"))
            yield db
            break
        except (OperationalError, DisconnectionError) as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {str(e)}")
            if db:
                db.close()
            
            if attempt < max_retries - 1:
                logger.info(f"Retrying database connection in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logger.error("All database connection attempts failed")
                raise
        except Exception as e:
            logger.error(f"Unexpected database error: {str(e)}")
            if db:
                db.close()
            raise
        finally:
            if db:
                db.close()

@contextmanager
def get_db_session():
    """Context manager for database sessions with automatic cleanup"""
    db = None
    try:
        db = SessionLocal()
        # Test the connection
        db.execute(text("SELECT 1"))
        yield db
    except (OperationalError, DisconnectionError) as e:
        logger.error(f"Database connection error: {str(e)}")
        if db:
            db.rollback()
        raise
    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        if db:
            db.rollback()
        raise
    finally:
        if db:
            db.close()

def test_database_connection():
    """Test database connection and log pool status"""
    try:
        with get_db_session() as db:
            result = db.execute(text("SELECT 1")).scalar()
            logger.info("Database connection test successful")
            
            # Log pool status
            pool = engine.pool
            logger.info(f"Database pool status: size={pool.size()}, checked_in={pool.checkedin()}, checked_out={pool.checkedout()}")
            
            return True
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        return False

def close_database_connections():
    """Close all database connections"""
    try:
        engine.dispose()
        logger.info("Database connections closed successfully")
    except Exception as e:
        logger.error(f"Error closing database connections: {str(e)}")

# Health check function for monitoring
def get_database_health():
    """Get database health status"""
    try:
        with get_db_session() as db:
            # Test basic connectivity
            db.execute(text("SELECT 1"))
            
            # Test a simple query
            result = db.execute(text("SELECT COUNT(*) FROM chatbots LIMIT 1")).scalar()
            
            pool = engine.pool
            return {
                "status": "healthy",
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "chatbot_count": result
            }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }