  podTemplate(
      containers: [
          containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat', ttyEnabled: true),
          containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
      ],
      imagePullSecrets: ['registry-credentials']) {
    properties([parameters(
        [string(name: 'dockerImageTag', defaultValue: 'latest', description: 'Docker image tag to deploy'),
         string(name: 'branchName', defaultValue: 'main', description: 'Branch being deployed'),
         string(name: 'triggeredByJob', defaultValue: 'manual', description: 'Information of which job triggered the build')])]
    )
    currentBuild.description = "branch ${params.branchName}-${params.dockerImageTag}"
    node(POD_LABEL) {
      container('helm') {
        withCredentials([[$class: 'FileBinding', credentialsId: 'kylas-stage-kubeconfig', variable: 'KUBECONFIG'],
                         [$class: 'StringBinding', credentialsId: 'sd-charts-github-api-token', variable: 'API_TOKEN']]) {
          stage('Add Helm repository') {
            sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
                label: 'Add stable helm repo'
            sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/master/'",
                label: 'Add helm repo'
            sh script: 'helm repo list', label: 'List available helm repos'
          }
          withCredentials([
                          [$class: 'StringBinding', credentialsId: 'stage-application-namespace', variable: 'APP_NAMESPACE'],
                          [$class: 'StringBinding', credentialsId: 'stage-env-postgres-password', variable: 'POSTGRES_PASSWORD'],
                          [$class: 'StringBinding', credentialsId: 'stage-env-postgres-username', variable: 'POSTGRES_USERNAME'],
                          [$class: 'StringBinding', credentialsId: 'stage-env-rabbitmq-password', variable: 'RABBITMQ_PASSWORD'],
                          [$class: 'StringBinding', credentialsId: 'kylas-test-openai-api-key', variable: 'OPENAI_API_KEY'],
                          [$class: 'StringBinding', credentialsId: 'stage-env-rabbitmq-username', variable: 'RABBITMQ_USERNAME'],
                          [$class: 'StringBinding', credentialsId: 'whatsapp-chatbot-knowledgebase-access-key-id', variable: 'KNOWLEDGEBASE_ACCESS_KEY_ID'],
                          [$class: 'StringBinding', credentialsId: 'whatsapp-chatbot-knowledgebase-access-key', variable: 'KNOWLEDGEBASE_ACCESS_KEY'],
                          [$class: 'StringBinding', credentialsId: 'qa-stage-redis-host-url', variable: 'REDIS_HOST']]) {
            stage('Deploy') {
              echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-whatsapp-chatbot:${params.dockerImageTag}"
              sh script: "helm upgrade --install sd-whatsapp-chatbot sd-charts/sd-whatsapp-chatbot " +
                  "--set "+
                  "appConfig.postgres.username=${POSTGRES_USERNAME},"+
                  "appConfig.postgres.password=${POSTGRES_PASSWORD},"+
                  "appConfig.postgres.hostname=postgresql,"+
                  "appConfig.postgres.flags=?sslmode=disable," +
                  "appConfig.rabbitmq.username=${RABBITMQ_USERNAME},"+
                  "appConfig.rabbitmq.password=${RABBITMQ_PASSWORD},"+
                  "appConfig.openai.apikey=${OPENAI_API_KEY}," +
                  "appConfig.redis.host=${REDIS_HOST}," +
                  "image.tag=${params.dockerImageTag}," +
                  "namespace=${APP_NAMESPACE}," +
                  "appConfig.activeProfile=STAGE," +
                  "appConfig.aws.accessKeyId=${KNOWLEDGEBASE_ACCESS_KEY_ID},"+
                  "appConfig.aws.secretAccessKey=${KNOWLEDGEBASE_ACCESS_KEY},"+
                  "appConfig.aws.region=sgp1,"+
                  "appConfig.aws.s3BucketName=whatsapp-chatbot-knowledgebase/stage,"+
                  "appConfig.digitalOcean.spacesKey=${KNOWLEDGEBASE_ACCESS_KEY_ID},"+
                  "appConfig.digitalOcean.spacesSecret=${KNOWLEDGEBASE_ACCESS_KEY},"+
                  "appConfig.digitalOcean.spacesRegion=sgp1,"+
                  "appConfig.digitalOcean.spacesBucket=whatsapp-chatbot-knowledgebase/stage,"+
                  "appConfig.digitalOcean.spacesEndpoint=https://sgp1.digitaloceanspaces.com,"+
                  "deployment.annotations.buildNumber=${currentBuild.number} " +
                  "--wait",
                  label: 'Install helm release'
            }
          }
        }
      }
      container('curl') {
        stage('Refresh Gateway routes') {
          sh script: 'curl -X POST \\\n' +
              '  https://api-stage.sling-dev.com/actuator/gateway/refresh \\\n' +
              '  -H \'Accept: application/json\' \\\n' +
              '  -H \'Host: api.sling-dev.com\' \\\n' +
              '  -H \'cache-control: no-cache\'', label: 'Force refresh routes cache'
        }

      }
      try {
          stage('Approval for Deployment') {
              userInput = input(id: 'confirm', message: 'Do you wish to deploy to PROD environment?',
                  parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy to PROD environment', name: 'confirm']])
          }
          stage('Start Prod Deployment') {
          build job: './deploy-to-prod',
              parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: params.dockerImageTag],
                           [$class: 'StringParameterValue', name: 'triggeredByJob', value: "deploy-to-prod : #${BUILD_NUMBER}"],
                           [$class: 'StringParameterValue', name: 'branchName', value: "${params.branchName}-${params.dockerImageTag}"]],
              wait: false
          }
      }
      catch (err) {
        def user = err.getCauses()[0].getUser()
        userInput = false
        echo "Aborted by: [${user}]"
      }
    }
  }
